#!/bin/bash

# Script to find existing ScalableTarget for ECS service
# Usage: ./find-scalable-target.sh <cluster-name> <service-name> [aws-profile]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

show_usage() {
    echo "Usage: $0 <cluster-name> <service-name> [aws-profile]"
    echo ""
    echo "Examples:"
    echo "  $0 Backend-API api-staging"
    echo "  $0 Backend-API api-staging my-aws-profile"
    echo ""
    echo "This script will find the existing ScalableTarget ARN for the specified ECS service."
}

# Check if required parameters are provided
if [ $# -lt 2 ]; then
    print_error "Missing required parameters"
    show_usage
    exit 1
fi

CLUSTER_NAME="$1"
SERVICE_NAME="$2"
AWS_PROFILE="${3:-}"

# Build AWS CLI arguments
AWS_ARGS=""
if [ -n "$AWS_PROFILE" ]; then
    AWS_ARGS="--profile $AWS_PROFILE"
fi

print_status "Searching for ScalableTarget for ECS service: $CLUSTER_NAME/$SERVICE_NAME"

# Query for the ScalableTarget
RESOURCE_ID="service/$CLUSTER_NAME/$SERVICE_NAME"

print_status "Resource ID: $RESOURCE_ID"

# Get ScalableTarget information
SCALABLE_TARGET=$(aws application-autoscaling describe-scalable-targets \
    --service-namespace ecs \
    --resource-ids "$RESOURCE_ID" \
    --scalable-dimension ecs:service:DesiredCount \
    $AWS_ARGS \
    --output json 2>/dev/null || echo "")

if [ -z "$SCALABLE_TARGET" ] || [ "$SCALABLE_TARGET" = "null" ]; then
    print_error "No ScalableTarget found for $RESOURCE_ID"
    print_status "This means either:"
    print_status "1. No auto-scaling is configured for this service"
    print_status "2. The cluster/service names are incorrect"
    print_status "3. You don't have permissions to view auto-scaling resources"
    exit 1
fi

# Parse the response
SCALABLE_TARGETS_COUNT=$(echo "$SCALABLE_TARGET" | jq -r '.ScalableTargets | length')

if [ "$SCALABLE_TARGETS_COUNT" -eq 0 ]; then
    print_error "No ScalableTarget found for $RESOURCE_ID"
    exit 1
fi

print_success "Found $SCALABLE_TARGETS_COUNT ScalableTarget(s)"

# Extract ScalableTarget details
SCALABLE_TARGET_ARN=$(echo "$SCALABLE_TARGET" | jq -r '.ScalableTargets[0].ScalableTargetARN')
MIN_CAPACITY=$(echo "$SCALABLE_TARGET" | jq -r '.ScalableTargets[0].MinCapacity')
MAX_CAPACITY=$(echo "$SCALABLE_TARGET" | jq -r '.ScalableTargets[0].MaxCapacity')
CREATION_TIME=$(echo "$SCALABLE_TARGET" | jq -r '.ScalableTargets[0].CreationTime')

echo ""
print_success "ScalableTarget Details:"
echo "  ARN: $SCALABLE_TARGET_ARN"
echo "  Resource ID: $RESOURCE_ID"
echo "  Min Capacity: $MIN_CAPACITY"
echo "  Max Capacity: $MAX_CAPACITY"
echo "  Creation Time: $CREATION_TIME"
echo ""

print_status "To use this ScalableTarget with your CloudFormation template:"
print_status "Update the ExistingScalableTargetId parameter in your parameters file:"
echo ""
echo "  \"ExistingScalableTargetId\": \"$SCALABLE_TARGET_ARN\""
echo ""

print_success "Script completed successfully!"