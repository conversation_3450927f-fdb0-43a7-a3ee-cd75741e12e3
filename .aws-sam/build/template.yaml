AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Multi-Metric Step Scaling for Existing ECS Service - Data-Intensive Application
Parameters:
  ExistingClusterName:
    Type: String
    Description: Name of the existing ECS cluster
    MinLength: 1
  ExistingServiceName:
    Type: String
    Description: Name of the existing ECS service
    MinLength: 1
  LoadBalancerFullName:
    Type: String
    Description: Full name of the Application Load Balancer (app/name/id)
    MinLength: 1
  TargetGroupFullName:
    Type: String
    Description: Full name of the Target Group
    MinLength: 1
  MinCapacity:
    Type: Number
    Default: 3
    MinValue: 1
    MaxValue: 100
    Description: Minimum number of tasks (higher for data processing stability)
  MaxCapacity:
    Type: Number
    Default: 15
    MinValue: 2
    MaxValue: 1000
    Description: Maximum number of tasks (conservative for cost control)
  CPUScaleUpThreshold:
    Type: Number
    Default: 55
    MinValue: 10
    MaxValue: 90
    Description: CPU threshold for scaling up (proactive)
  CPUScaleDownThreshold:
    Type: Number
    Default: 35
    MinValue: 5
    MaxValue: 80
    Description: CPU threshold for scaling down (conservative)
  MemoryScaleUpThreshold:
    Type: Number
    Default: 55
    MinValue: 10
    MaxValue: 90
    Description: Memory threshold for scaling up (proactive)
  MemoryScaleDownThreshold:
    Type: Number
    Default: 35
    MinValue: 5
    MaxValue: 80
    Description: Memory threshold for scaling down (conservative)
  BaseRPMPerTask:
    Type: Number
    Default: 4750
    MinValue: 50
    MaxValue: 10000
    Description: Base requests per minute per task for scaling calculations
  HighRequestCountMultiplier:
    Type: Number
    Default: 1.0
    MinValue: 0.5
    MaxValue: 3.0
    Description: Multiplier for high request count threshold (1.0 = 300 RPM per task)
  LowRequestCountMultiplier:
    Type: Number
    Default: 0.17
    MinValue: 0.05
    MaxValue: 0.5
    Description: Multiplier for low request count threshold (0.17 = ~50 RPM per task)
  CPUMemoryScaleUpCooldown:
    Type: Number
    Default: 60
    MinValue: 60
    MaxValue: 3600
    Description: Scale-up cooldown for CPU/Memory in seconds (1 minute)
  ALBScaleUpCooldown:
    Type: Number
    Default: 60
    MinValue: 60
    MaxValue: 1800
    Description: Scale-up cooldown for ALB requests in seconds (1 minute)
  ScaleDownCooldown:
    Type: Number
    Default: 900
    MinValue: 300
    MaxValue: 3600
    Description: Scale-down cooldown in seconds (15 minutes)
  NotificationEmail:
    Type: String
    Default: ''
    Description: Email address for scaling notifications (optional)
  Environment:
    Type: String
    Default: dev
    AllowedValues:
    - dev
    - staging
    - prod
    Description: Environment name for resource tagging
  CreateScalableTarget:
    Type: String
    Default: 'true'
    AllowedValues:
    - 'true'
    - 'false'
    Description: Whether to create a new ScalableTarget (set to false if one already
      exists)
  ExistingScalableTargetId:
    Type: String
    Default: ''
    Description: ARN of existing ScalableTarget (required when CreateScalableTarget
      is false)
Conditions:
  HasNotificationEmail:
    Fn::Not:
    - Fn::Equals:
      - Ref: NotificationEmail
      - ''
  ShouldCreateScalableTarget:
    Fn::Equals:
    - Ref: CreateScalableTarget
    - 'true'
Resources:
  ScalingNotificationTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName:
        Fn::Sub: ${ExistingServiceName}-scaling-notifications
      DisplayName:
        Fn::Sub: ECS Scaling Notifications for ${ExistingServiceName}
      Tags:
      - Key: Environment
        Value:
          Ref: Environment
      - Key: Service
        Value:
          Ref: ExistingServiceName
  ScalingNotificationSubscriptionV2:
    Type: AWS::SNS::Subscription
    Condition: HasNotificationEmail
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete
    Properties:
      TopicArn:
        Ref: ScalingNotificationTopic
      Protocol: email
      Endpoint:
        Ref: NotificationEmail
      DeliveryPolicy:
        healthyRetryPolicy:
          numRetries: 3
          minDelayTarget: 20
          maxDelayTarget: 20
  ScalableTarget:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Condition: ShouldCreateScalableTarget
    Properties:
      ServiceNamespace: ecs
      ResourceId:
        Fn::Sub: service/${ExistingClusterName}/${ExistingServiceName}
      ScalableDimension: ecs:service:DesiredCount
      MinCapacity:
        Ref: MinCapacity
      MaxCapacity:
        Ref: MaxCapacity
      RoleARN:
        Fn::Sub: arn:aws:iam::${AWS::AccountId}:role/aws-service-role/ecs.application-autoscaling.amazonaws.com/AWSServiceRoleForApplicationAutoScaling_ECSService
  MemoryScaleUpPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName:
        Fn::Sub: ${ExistingServiceName}-MemoryScaleUpPolicy
      PolicyType: StepScaling
      ScalingTargetId:
        Fn::If:
        - ShouldCreateScalableTarget
        - Ref: ScalableTarget
        - Ref: ExistingScalableTargetId
      StepScalingPolicyConfiguration:
        AdjustmentType: ChangeInCapacity
        Cooldown:
          Ref: CPUMemoryScaleUpCooldown
        MetricAggregationType: Average
        StepAdjustments:
        - MetricIntervalLowerBound: 0
          MetricIntervalUpperBound: 10
          ScalingAdjustment: 5
        - MetricIntervalLowerBound: 10
          MetricIntervalUpperBound: 20
          ScalingAdjustment: 10
        - MetricIntervalLowerBound: 20
          ScalingAdjustment: 15
  CPUScaleUpPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName:
        Fn::Sub: ${ExistingServiceName}-CPUScaleUpPolicy
      PolicyType: StepScaling
      ScalingTargetId:
        Fn::If:
        - ShouldCreateScalableTarget
        - Ref: ScalableTarget
        - Ref: ExistingScalableTargetId
      StepScalingPolicyConfiguration:
        AdjustmentType: ChangeInCapacity
        Cooldown:
          Ref: CPUMemoryScaleUpCooldown
        MetricAggregationType: Average
        StepAdjustments:
        - MetricIntervalLowerBound: 0
          MetricIntervalUpperBound: 10
          ScalingAdjustment: 5
        - MetricIntervalLowerBound: 10
          MetricIntervalUpperBound: 15
          ScalingAdjustment: 10
        - MetricIntervalLowerBound: 15
          ScalingAdjustment: 15
  ALBRequestScaleUpPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName:
        Fn::Sub: ${ExistingServiceName}-ALBRequestScaleUpPolicy
      PolicyType: StepScaling
      ScalingTargetId:
        Fn::If:
        - ShouldCreateScalableTarget
        - Ref: ScalableTarget
        - Ref: ExistingScalableTargetId
      StepScalingPolicyConfiguration:
        AdjustmentType: ChangeInCapacity
        Cooldown:
          Ref: ALBScaleUpCooldown
        MetricAggregationType: Average
        StepAdjustments:
        - MetricIntervalLowerBound: 0
          MetricIntervalUpperBound: 2000
          ScalingAdjustment: 5
        - MetricIntervalLowerBound: 2000
          MetricIntervalUpperBound: 4000
          ScalingAdjustment: 10
        - MetricIntervalLowerBound: 4000
          MetricIntervalUpperBound: 8000
          ScalingAdjustment: 15
        - MetricIntervalLowerBound: 8000
          ScalingAdjustment: 20
  ScaleDownPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName:
        Fn::Sub: ${ExistingServiceName}-ConservativeScaleDownPolicy
      PolicyType: StepScaling
      ScalingTargetId:
        Fn::If:
        - ShouldCreateScalableTarget
        - Ref: ScalableTarget
        - Ref: ExistingScalableTargetId
      StepScalingPolicyConfiguration:
        AdjustmentType: ChangeInCapacity
        Cooldown:
          Ref: ScaleDownCooldown
        MetricAggregationType: Average
        StepAdjustments:
        - MetricIntervalUpperBound: -10
          MetricIntervalLowerBound: -20
          ScalingAdjustment: -1
        - MetricIntervalUpperBound: -20
          MetricIntervalLowerBound: -30
          ScalingAdjustment: -1
        - MetricIntervalUpperBound: -30
          ScalingAdjustment: -2
  EmergencyScaleUpPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName:
        Fn::Sub: ${ExistingServiceName}-EmergencyScaleUpPolicy
      PolicyType: StepScaling
      ScalingTargetId:
        Fn::If:
        - ShouldCreateScalableTarget
        - Ref: ScalableTarget
        - Ref: ExistingScalableTargetId
      StepScalingPolicyConfiguration:
        AdjustmentType: ChangeInCapacity
        Cooldown: 180
        MetricAggregationType: Average
        StepAdjustments:
        - MetricIntervalLowerBound: 0
          ScalingAdjustment: 5
  HighMemoryAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName:
        Fn::Sub: ${ExistingServiceName}-HighMemoryUtilization
      AlarmDescription: Memory utilization is high for data processing service
      MetricName: MemoryUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: 60
      EvaluationPeriods: 2
      Threshold:
        Ref: MemoryScaleUpThreshold
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
      - Name: ServiceName
        Value:
          Ref: ExistingServiceName
      - Name: ClusterName
        Value:
          Ref: ExistingClusterName
      AlarmActions:
      - Ref: MemoryScaleUpPolicy
      - Ref: ScalingNotificationTopic
  TaskCountAboveMinimumAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName:
        Fn::Sub: ${ExistingServiceName}-TaskCountAboveMinimum
      AlarmDescription: Current task count is above minimum - safe to scale down
      MetricName: RunningTaskCount
      Namespace: AWS/ECS
      Statistic: Average
      Period: 60
      EvaluationPeriods: 1
      Threshold:
        Ref: MinCapacity
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
      - Name: ServiceName
        Value:
          Ref: ExistingServiceName
      - Name: ClusterName
        Value:
          Ref: ExistingClusterName
      TreatMissingData: breaching
  LowMemoryAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName:
        Fn::Sub: ${ExistingServiceName}-LowMemoryUtilization
      AlarmDescription: Memory utilization is low - safe to scale down
      MetricName: MemoryUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 4
      Threshold:
        Ref: MemoryScaleDownThreshold
      ComparisonOperator: LessThanThreshold
      Dimensions:
      - Name: ServiceName
        Value:
          Ref: ExistingServiceName
      - Name: ClusterName
        Value:
          Ref: ExistingClusterName
      TreatMissingData: notBreaching
  HighCPUAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName:
        Fn::Sub: ${ExistingServiceName}-HighCPUUtilization
      AlarmDescription: CPU utilization is high for data processing service
      MetricName: CPUUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: 60
      EvaluationPeriods: 2
      Threshold:
        Ref: CPUScaleUpThreshold
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
      - Name: ServiceName
        Value:
          Ref: ExistingServiceName
      - Name: ClusterName
        Value:
          Ref: ExistingClusterName
      AlarmActions:
      - Ref: CPUScaleUpPolicy
      - Ref: ScalingNotificationTopic
  LowCPUAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName:
        Fn::Sub: ${ExistingServiceName}-LowCPUUtilization
      AlarmDescription: CPU utilization is low - safe to scale down
      MetricName: CPUUtilization
      Namespace: AWS/ECS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 4
      Threshold:
        Ref: CPUScaleDownThreshold
      ComparisonOperator: LessThanThreshold
      Dimensions:
      - Name: ServiceName
        Value:
          Ref: ExistingServiceName
      - Name: ClusterName
        Value:
          Ref: ExistingClusterName
      TreatMissingData: notBreaching
  HighRequestCountAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName:
        Fn::Sub: ${ExistingServiceName}-HighRequestCount
      AlarmDescription: ALB request count is above 2000 requests/min - scale up needed
      MetricName: RequestCount
      Namespace: AWS/ApplicationELB
      Statistic: Sum
      Period: 60
      EvaluationPeriods: 2
      Threshold: 2000
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
      - Name: LoadBalancer
        Value:
          Ref: LoadBalancerFullName
      AlarmActions:
      - Ref: ALBRequestScaleUpPolicy
      - Ref: ScalingNotificationTopic
  LowRequestCountAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName:
        Fn::Sub: ${ExistingServiceName}-LowRequestCount
      AlarmDescription: ALB request count is below 1000 requests/min for 10 minutes
        - safe to scale down
      MetricName: RequestCount
      Namespace: AWS/ApplicationELB
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 5000
      ComparisonOperator: LessThanThreshold
      Dimensions:
      - Name: LoadBalancer
        Value:
          Ref: LoadBalancerFullName
      TreatMissingData: notBreaching
  CompositeScaleUpAlarm:
    Type: AWS::CloudWatch::CompositeAlarm
    Properties:
      AlarmName:
        Fn::Sub: ${ExistingServiceName}-CompositeScaleUp
      AlarmDescription: Scale up when CPU, Memory, or Request Count is high
      AlarmRule:
        Fn::Sub: ALARM("${HighCPUAlarm}") OR ALARM("${HighMemoryAlarm}") OR ALARM("${HighRequestCountAlarm}")
      ActionsEnabled: true
      AlarmActions:
      - Ref: ScalingNotificationTopic
  CompositeScaleDownAlarm:
    Type: AWS::CloudWatch::CompositeAlarm
    Properties:
      AlarmName:
        Fn::Sub: ${ExistingServiceName}-CompositeScaleDown
      AlarmDescription: Scale down only when CPU, Memory, AND Request Count are all
        low AND task count is above minimum
      AlarmRule:
        Fn::Sub: ALARM("${LowCPUAlarm}") AND ALARM("${LowMemoryAlarm}") AND ALARM("${LowRequestCountAlarm}")
          AND ALARM("${TaskCountAboveMinimumAlarm}")
      ActionsEnabled: true
      AlarmActions:
      - Ref: ScalingNotificationTopic
  ScaleDownTriggerAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName:
        Fn::Sub: ${ExistingServiceName}-ScaleDownTrigger
      AlarmDescription: 'Triggers scale down when all conditions are met: low CPU,
        low memory, low requests, and task count above minimum'
      ComparisonOperator: LessThanThreshold
      EvaluationPeriods: 1
      Threshold: 1
      TreatMissingData: notBreaching
      Metrics:
      - Id: m1
        ReturnData: false
        MetricStat:
          Metric:
            MetricName: CPUUtilization
            Namespace: AWS/ECS
            Dimensions:
            - Name: ServiceName
              Value:
                Ref: ExistingServiceName
            - Name: ClusterName
              Value:
                Ref: ExistingClusterName
          Period: 300
          Stat: Average
      - Id: m2
        ReturnData: false
        MetricStat:
          Metric:
            MetricName: MemoryUtilization
            Namespace: AWS/ECS
            Dimensions:
            - Name: ServiceName
              Value:
                Ref: ExistingServiceName
            - Name: ClusterName
              Value:
                Ref: ExistingClusterName
          Period: 300
          Stat: Average
      - Id: m3
        ReturnData: false
        MetricStat:
          Metric:
            MetricName: RequestCount
            Namespace: AWS/ApplicationELB
            Dimensions:
            - Name: LoadBalancer
              Value:
                Ref: LoadBalancerFullName
          Period: 300
          Stat: Sum
      - Id: m4
        ReturnData: false
        MetricStat:
          Metric:
            MetricName: RunningTaskCount
            Namespace: AWS/ECS
            Dimensions:
            - Name: ServiceName
              Value:
                Ref: ExistingServiceName
            - Name: ClusterName
              Value:
                Ref: ExistingClusterName
          Period: 300
          Stat: Average
      - Id: e1
        ReturnData: true
        Expression:
          Fn::Sub: IF(m1 < ${CPUScaleDownThreshold} AND m2 < ${MemoryScaleDownThreshold}
            AND m3 < 5000 AND m4 > ${MinCapacity}, 0, 1)
      AlarmActions:
      - Ref: ScaleDownPolicy
  EmergencyScaleUpAlarm:
    Type: AWS::CloudWatch::CompositeAlarm
    Properties:
      AlarmName:
        Fn::Sub: ${ExistingServiceName}-EmergencyScaleUp
      AlarmDescription: Emergency scaling when multiple metrics are critically high
      AlarmRule:
        Fn::Sub: (ALARM("${HighCPUAlarm}") AND ALARM("${HighMemoryAlarm}")) OR (ALARM("${HighCPUAlarm}")
          AND ALARM("${HighRequestCountAlarm}")) OR (ALARM("${HighMemoryAlarm}") AND
          ALARM("${HighRequestCountAlarm}"))
      ActionsEnabled: true
      AlarmActions:
      - Ref: ScalingNotificationTopic
  RequestThresholdCalculatorRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName:
        Fn::Sub: ${ExistingServiceName}-RequestThresholdCalculatorRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
        - Effect: Allow
          Principal:
            Service: lambda.amazonaws.com
          Action: sts:AssumeRole
      ManagedPolicyArns:
      - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
      - PolicyName: ECSAndCloudWatchAccess
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
          - Effect: Allow
            Action:
            - ecs:DescribeServices
            - ecs:ListServices
            - cloudwatch:PutMetricAlarm
            - cloudwatch:DescribeAlarms
            Resource: '*'
  RequestThresholdCalculator:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName:
        Fn::Sub: ${ExistingServiceName}-RequestThresholdCalculator
      CodeUri: RequestThresholdCalculator
      Handler: threshold_calculator.lambda_handler
      Runtime: python3.9
      Timeout: 60
      MemorySize: 128
      Role:
        Fn::GetAtt:
        - RequestThresholdCalculatorRole
        - Arn
      Environment:
        Variables:
          CLUSTER_NAME:
            Ref: ExistingClusterName
          SERVICE_NAME:
            Ref: ExistingServiceName
          BASE_RPM_PER_TASK:
            Ref: BaseRPMPerTask
          HIGH_REQUEST_MULTIPLIER:
            Ref: HighRequestCountMultiplier
          LOW_REQUEST_MULTIPLIER:
            Ref: LowRequestCountMultiplier
      Tags:
        Environment:
          Ref: Environment
        Service:
          Ref: ExistingServiceName
    Metadata:
      SamResourceId: RequestThresholdCalculator
  MultiMetricDashboard:
    Type: AWS::CloudWatch::Dashboard
    Properties:
      DashboardName:
        Fn::Sub: ${ExistingServiceName}-MultiMetric-Scaling
      DashboardBody:
        Fn::Sub: "{\n  \"widgets\": [\n    {\n      \"type\": \"metric\",\n      \"\
          x\": 0,\n      \"y\": 0,\n      \"width\": 12,\n      \"height\": 6,\n \
          \     \"properties\": {\n        \"metrics\": [\n          [\"AWS/ECS\"\
          , \"CPUUtilization\", \"ServiceName\", \"${ExistingServiceName}\", \"ClusterName\"\
          , \"${ExistingClusterName}\"],\n          [\".\", \"MemoryUtilization\"\
          , \".\", \".\", \".\", \".\"],\n          [\"AWS/ApplicationELB\", \"RequestCount\"\
          , \"LoadBalancer\", \"${LoadBalancerFullName}\"]\n        ],\n        \"\
          period\": 300,\n        \"stat\": \"Average\",\n        \"region\": \"${AWS::Region}\"\
          ,\n        \"title\": \"Multi-Metric Scaling Overview\",\n        \"yAxis\"\
          : {\n          \"left\": {\n            \"min\": 0,\n            \"max\"\
          : 100\n          }\n        }\n      }\n    },\n    {\n      \"type\": \"\
          metric\",\n      \"x\": 12,\n      \"y\": 0,\n      \"width\": 12,\n   \
          \   \"height\": 6,\n      \"properties\": {\n        \"metrics\": [\n  \
          \        [\"ECS/ContainerInsights\", \"RunningTaskCount\", \"ServiceName\"\
          , \"${ExistingServiceName}\", \"ClusterName\", \"${ExistingClusterName}\"\
          ],\n          [\"ECS/ContainerInsights\", \"DeploymentCount\", \"ServiceName\"\
          , \"${ExistingServiceName}\", \"ClusterName\", \"${ExistingClusterName}\"\
          ]\n        ],\n        \"period\": 300,\n        \"stat\": \"Average\",\n\
          \        \"region\": \"${AWS::Region}\",\n        \"title\": \"Task Count\
          \ and Scaling Activities\"\n      }\n    },\n    {\n      \"type\": \"metric\"\
          ,\n      \"x\": 0,\n      \"y\": 6,\n      \"width\": 12,\n      \"height\"\
          : 6,\n      \"properties\": {\n        \"metrics\": [\n          [\"AWS/ApplicationELB\"\
          , \"TargetResponseTime\", \"LoadBalancer\", \"${LoadBalancerFullName}\"\
          ],\n          [\".\", \"HTTPCode_Target_2XX_Count\", \".\", \".\"],\n  \
          \        [\".\", \"HTTPCode_Target_5XX_Count\", \".\", \".\"]\n        ],\n\
          \        \"period\": 300,\n        \"stat\": \"Average\",\n        \"region\"\
          : \"${AWS::Region}\",\n        \"title\": \"ALB Performance Metrics\"\n\
          \      }\n    },\n    {\n      \"type\": \"metric\",\n      \"x\": 12,\n\
          \      \"y\": 6,\n      \"width\": 12,\n      \"height\": 6,\n      \"properties\"\
          : {\n        \"metrics\": [\n          [\"AWS/Logs\", \"IncomingLogEvents\"\
          , \"LogGroupName\", \"/ecs/api-production\"]\n        ],\n        \"period\"\
          : 300,\n        \"stat\": \"Sum\",\n        \"region\": \"${AWS::Region}\"\
          ,\n        \"title\": \"Application Log Activity\"\n      }\n    }\n  ]\n\
          }\n"
Outputs:
  ScalableTargetId:
    Description: Application Auto Scaling Target ID
    Value:
      Fn::If:
      - ShouldCreateScalableTarget
      - Ref: ScalableTarget
      - Ref: ExistingScalableTargetId
    Export:
      Name:
        Fn::Sub: ${AWS::StackName}-ScalableTargetId
  ScalingNotificationTopicArn:
    Description: SNS Topic ARN for scaling notifications
    Value:
      Ref: ScalingNotificationTopic
    Export:
      Name:
        Fn::Sub: ${AWS::StackName}-ScalingNotificationTopicArn
  DashboardURL:
    Description: CloudWatch Dashboard URL
    Value:
      Fn::Sub: https://${AWS::Region}.console.aws.amazon.com/cloudwatch/home?region=${AWS::Region}#dashboards:name=${ExistingServiceName}-MultiMetric-Scaling
  RequestThresholdCalculatorFunctionName:
    Description: Lambda function name for request threshold calculation
    Value:
      Ref: RequestThresholdCalculator
    Export:
      Name:
        Fn::Sub: ${AWS::StackName}-RequestThresholdCalculatorFunctionName
  CompositeAlarms:
    Description: Composite alarm names for monitoring
    Value:
      Fn::Sub: ${CompositeScaleUpAlarm},${CompositeScaleDownAlarm},${EmergencyScaleUpAlarm}
