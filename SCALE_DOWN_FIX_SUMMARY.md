# Scale Down Fix Summary

## Issues Identified

The scale down functionality wasn't working due to several configuration issues:

### 1. **Scale Down Policy Step Adjustments Were Incorrect**
- **Problem**: The original `ScaleDownPolicy` used negative `MetricIntervalLowerBound` and `MetricIntervalUpperBound` values (-10, -20, -30), but the `ScaleDownTriggerAlarm` returns 0 when scale down conditions are met.
- **Fix**: Changed the step adjustment to use `MetricIntervalUpperBound: 0` to properly handle the alarm's 0/1 logic.

### 2. **CompositeScaleDownAlarm Only Sent Notifications**
- **Problem**: The `CompositeScaleDownAlarm` was only configured to send notifications, not trigger actual scaling actions.
- **Fix**: Added a new `SimpleScaleDownPolicy` and connected it to the `CompositeScaleDownAlarm`.

### 3. **Complex Logic Mismatch**
- **Problem**: Composite alarms can't directly trigger step scaling policies with complex metric math expressions.
- **Fix**: Created two separate scale down mechanisms:
  - `SimpleScaleDownPolicy`: Simple scaling policy for composite alarms
  - `ScaleDownPolicy`: Step scaling policy for metric math alarms

## Changes Made

### 1. **Added SimpleScaleDownPolicy**
```yaml
SimpleScaleDownPolicy:
  Type: AWS::ApplicationAutoScaling::ScalingPolicy
  Properties:
    PolicyType: SimpleScaling
    AdjustmentType: ChangeInCapacity
    ScalingAdjustment: -1
    Cooldown: !Ref ScaleDownCooldown
```

### 2. **Fixed ScaleDownPolicy Step Adjustments**
```yaml
StepAdjustments:
  - MetricIntervalUpperBound: 0
    ScalingAdjustment: -1   # When trigger alarm returns 0: -1 task
```

### 3. **Connected CompositeScaleDownAlarm to SimpleScaleDownPolicy**
```yaml
AlarmActions:
  - !Ref SimpleScaleDownPolicy
  - !Ref ScalingNotificationTopic
```

### 4. **Added Debug Alarm**
- `ScaleDownConditionsDebugAlarm`: Monitors when scale down conditions are met and sends notifications for debugging.

## Scale Down Triggers

Now there are **two ways** scale down can be triggered:

### 1. **Composite Alarm Trigger** (Recommended)
- **Alarm**: `CompositeScaleDownAlarm`
- **Policy**: `SimpleScaleDownPolicy`
- **Conditions**: ALL must be true:
  - Low CPU (< 35% for 20 minutes)
  - Low Memory (< 35% for 20 minutes)  
  - Low Request Count (< 5000 requests per 5-min period for 10 minutes)
  - Task count above minimum

### 2. **Metric Math Trigger** (Backup)
- **Alarm**: `ScaleDownTriggerAlarm`
- **Policy**: `ScaleDownPolicy`
- **Conditions**: Same as above but using metric math expression

## Troubleshooting Scale Down Issues

### 1. **Check Alarm States**
```bash
aws cloudwatch describe-alarms --alarm-names \
  "YourService-CompositeScaleDown" \
  "YourService-ScaleDownTrigger" \
  "YourService-ScaleDownConditionsDebug" \
  "YourService-LowCPUUtilization" \
  "YourService-LowMemoryUtilization" \
  "YourService-LowRequestCount" \
  "YourService-TaskCountAboveMinimum"
```

### 2. **Check Individual Conditions**
- **CPU**: Must be < 35% for 20 minutes (4 periods of 5 minutes)
- **Memory**: Must be < 35% for 20 minutes (4 periods of 5 minutes)
- **Requests**: Must be < 5000 per 5-minute period for 10 minutes (2 periods)
- **Task Count**: Must be > MinCapacity

### 3. **Check Scaling History**
```bash
aws application-autoscaling describe-scaling-activities \
  --service-namespace ecs \
  --resource-id "service/YourCluster/YourService"
```

### 4. **Monitor Debug Alarm**
The `ScaleDownConditionsDebugAlarm` will:
- Return 0 when ALL scale down conditions are met
- Return 1 when any condition is NOT met
- Send notifications when conditions change

### 5. **Check Cooldown Periods**
- Scale down cooldown: 15 minutes (900 seconds) by default
- If a recent scaling activity occurred, wait for cooldown to expire

## Key Parameters

- **CPUScaleDownThreshold**: 35% (default)
- **MemoryScaleDownThreshold**: 35% (default)
- **Request Count Threshold**: 5000 requests per 5-minute period
- **ScaleDownCooldown**: 900 seconds (15 minutes)
- **MinCapacity**: 3 tasks (default)

## Verification Steps

1. **Deploy the updated template**
2. **Wait for low utilization conditions** (all metrics below thresholds)
3. **Check that CompositeScaleDownAlarm goes to ALARM state**
4. **Verify scale down action occurs** within a few minutes
5. **Monitor scaling notifications** via SNS topic

## Expected Behavior

When all conditions are met:
1. Individual alarms (`LowCPUAlarm`, `LowMemoryAlarm`, etc.) go to ALARM state
2. `CompositeScaleDownAlarm` goes to ALARM state
3. `SimpleScaleDownPolicy` is triggered
4. Task count decreases by 1
5. Cooldown period begins (15 minutes)
6. Notifications are sent via SNS
