# ECS Scaling Issue Fix Summary

## Issue Description
The ECS service `api-prod` was experiencing scaling failures with the following errors:
- `AlreadyAtMinCapacity` - Service attempting to scale down when already at minimum capacity (5 tasks)
- `TargetServicePutResourceAsUnscalable` - Related scaling constraint violations

The errors were triggered by:
- CloudWatch alarm: `api-prod-LowCPUUtilization` 
- Scaling policy: `api-prod-ConservativeScaleDownPolicy`

## Root Cause Analysis
The issue occurred because the individual CloudWatch alarms (`LowCPUAlarm`, `LowMemoryAlarm`, `LowRequestCountAlarm`) were directly triggering the `ScaleDownPolicy` without checking if the service was already at minimum capacity. When CPU utilization dropped below 35%, the alarm would trigger scale-down attempts even when the service was already running the minimum number of tasks (5).

## Solution Implemented

### 1. Added Task Count Monitoring
- **New Alarm**: `TaskCountAboveMinimumAlarm`
  - Monitors `RunningTaskCount` metric from AWS/ECS
  - Only triggers when current task count > MinCapacity
  - Period: 60 seconds, Evaluation: 1 period
  - Threshold: MinCapacity value (5 for prod)

### 2. Modified Individual Alarms
Updated the following alarms to remove direct ScaleDownPolicy triggers:
- `LowCPUAlarm` - Removed direct ScaleDownPolicy action
- `LowMemoryAlarm` - Removed direct ScaleDownPolicy action  
- `LowRequestCountAlarm` - Removed direct ScaleDownPolicy action
- Added `TreatMissingData: notBreaching` for better reliability

### 3. Enhanced Composite Alarm Logic
Modified `CompositeScaleDownAlarm` to require ALL four conditions:
1. CPU utilization is low (`LowCPUAlarm`)
2. Memory utilization is low (`LowMemoryAlarm`) 
3. Request count is low (`LowRequestCountAlarm`)
4. **NEW**: Task count is above minimum (`TaskCountAboveMinimumAlarm`)

The composite alarm now triggers `ScaleDownPolicy` only when all conditions are met, preventing scale-down attempts when already at minimum capacity.

## Changes Made to Files

### template.yaml
1. **Added TaskCountAboveMinimumAlarm** (lines 309-327):
   ```yaml
   TaskCountAboveMinimumAlarm:
     Type: AWS::CloudWatch::Alarm
     Properties:
       AlarmName: !Sub "${ExistingServiceName}-TaskCountAboveMinimum"
       MetricName: RunningTaskCount
       Namespace: AWS/ECS
       Threshold: !Ref MinCapacity
       ComparisonOperator: GreaterThanThreshold
   ```

2. **Updated Individual Alarms**:
   - Removed `AlarmActions: [!Ref ScaleDownPolicy]` from LowCPUAlarm, LowMemoryAlarm, LowRequestCountAlarm
   - Added `TreatMissingData: notBreaching` to all low utilization alarms

3. **Enhanced CompositeScaleDownAlarm** (lines 437-446):
   ```yaml
   CompositeScaleDownAlarm:
     Properties:
       AlarmRule: !Sub "ALARM(\"${LowCPUAlarm}\") AND ALARM(\"${LowMemoryAlarm}\") AND ALARM(\"${LowRequestCountAlarm}\") AND ALARM(\"${TaskCountAboveMinimumAlarm}\")"
       AlarmActions:
         - !Ref ScaleDownPolicy
         - !Ref ScalingNotificationTopic
   ```

## Deployment Instructions

### Prerequisites
- AWS CLI configured with appropriate permissions
- SAM CLI installed
- Access to the production AWS account

### Validation
```bash
# Validate the template syntax
aws cloudformation validate-template --template-body file://template.yaml
```

### Deployment
```bash
# Deploy to production environment
./deploy.sh -e prod -r eu-west-1 --profile production

# Or with explicit parameters
./deploy.sh -e prod -s ecs-scaling-prod -p parameters/prod.json --profile production
```

### Verification Steps
After deployment, verify the fix by:

1. **Check Alarm States**:
   ```bash
   aws cloudwatch describe-alarms --alarm-names "api-prod-TaskCountAboveMinimum" "api-prod-CompositeScaleDown"
   ```

2. **Monitor ECS Scaling Activity**:
   - Go to ECS Console → Clusters → Backend-API → Services → api-prod
   - Check "Scaling" tab for any new scaling activities
   - Verify no more "AlreadyAtMinCapacity" errors occur

3. **Test Scenario**:
   - Wait for low CPU utilization period
   - Confirm that scale-down only occurs when task count > 5
   - Verify no scaling attempts when at minimum capacity

## Expected Behavior After Fix

### Before Fix
- Low CPU → Direct scale-down attempt → "AlreadyAtMinCapacity" error when at 5 tasks

### After Fix  
- Low CPU + Low Memory + Low Requests + Task Count > 5 → Scale-down succeeds
- Low CPU + Low Memory + Low Requests + Task Count = 5 → No scale-down attempt

## Monitoring and Alerts
The solution maintains all existing monitoring capabilities:
- Scale-up policies remain unchanged
- Notification emails still sent for all scaling events
- CloudWatch dashboard continues to show all metrics
- Composite alarms provide intelligent scaling decisions

## Risk Assessment
- **Low Risk**: Changes only affect scale-down logic, scale-up remains unchanged
- **Backward Compatible**: No breaking changes to existing functionality
- **Fail-Safe**: If TaskCountAboveMinimumAlarm fails, scale-down is prevented (safer than allowing it)

## Rollback Plan
If issues occur, rollback by reverting the CompositeScaleDownAlarm to its previous state:
```bash
git checkout HEAD~1 template.yaml
./deploy.sh -e prod
```