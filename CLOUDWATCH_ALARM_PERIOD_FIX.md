# CloudWatch Alarm Period Mismatch Fix

## Issue Description
CloudFormation deployment failed with the following error:
```
CREATE_FAILED                AWS::CloudWatch::Alarm       ScaleDownTriggerAlarm        Resource handler returned  
                                                                                       message: "All metrics in   
                                                                                       the alarm should have the  
                                                                                       same period (Service:      
                                                                                       CloudWatch, Status Code:   
                                                                                       400, Request ID: a884f575- 
                                                                                       8c9b-4481-b210-            
                                                                                       40589aba8685)"             
```

## Root Cause Analysis
The `ScaleDownTriggerAlarm` was configured with metrics that had inconsistent period values:

- **m1 (CPUUtilization)**: Period: 300 seconds ✓
- **m2 (MemoryUtilization)**: Period: 300 seconds ✓  
- **m3 (RequestCount)**: Period: 300 seconds ✓
- **m4 (RunningTaskCount)**: Period: 60 seconds ❌

AWS CloudWatch requires all metrics within a single alarm to have the same period value. The RunningTaskCount metric had a different period (60 seconds) compared to the other three metrics (300 seconds), causing the validation error.

## Solution Implemented

### Fix Applied
Changed the RunningTaskCount metric period from 60 seconds to 300 seconds to match all other metrics in the alarm.

**Before:**
```yaml
- Id: m4
  ReturnData: false
  MetricStat:
    Metric:
      MetricName: RunningTaskCount
      Namespace: AWS/ECS
      Dimensions:
        - Name: ServiceName
          Value: !Ref ExistingServiceName
        - Name: ClusterName
          Value: !Ref ExistingClusterName
    Period: 60  # ❌ Different from other metrics
    Stat: Average
```

**After:**
```yaml
- Id: m4
  ReturnData: false
  MetricStat:
    Metric:
      MetricName: RunningTaskCount
      Namespace: AWS/ECS
      Dimensions:
        - Name: ServiceName
          Value: !Ref ExistingServiceName
        - Name: ClusterName
          Value: !Ref ExistingClusterName
    Period: 300  # ✓ Now matches other metrics
    Stat: Average
```

## Impact Assessment

### Functional Impact
- **No Logic Change**: The scaling logic remains exactly the same
- **Slightly Less Responsive**: RunningTaskCount now evaluated every 5 minutes instead of 1 minute
- **Still Effective**: 5-minute intervals are sufficient for task count monitoring in scaling decisions
- **Maintains Safety**: Scale-down prevention when at minimum capacity still works correctly

### Performance Impact
- **Minimal**: Task count changes are typically gradual, so 5-minute intervals are adequate
- **Consistent**: All metrics now evaluated on the same timeline for better correlation
- **Reliable**: Reduces potential timing issues between different metric periods

## Changes Made

### template.yaml
**Line 506**: Changed `Period: 60` to `Period: 300` for the RunningTaskCount metric in ScaleDownTriggerAlarm

```diff
- Period: 60
+ Period: 300
```

## Validation Results

### Template Validation
✅ CloudFormation template syntax validation passed
✅ All metrics in ScaleDownTriggerAlarm now have Period: 300
✅ Metric math expression remains unchanged and functional

### Expected Behavior
- **Before Fix**: CloudFormation CREATE_FAILED due to period mismatch
- **After Fix**: ScaleDownTriggerAlarm creates successfully with consistent 300-second periods

## Deployment Instructions

### Prerequisites
- AWS CLI configured with appropriate permissions
- Access to the target AWS account and region

### Validation Steps
```bash
# 1. Validate template syntax
aws cloudformation validate-template --template-body file://template.yaml

# 2. Verify all metrics have the same period
grep -A 50 "ScaleDownTriggerAlarm:" template.yaml | grep "Period:" | sort -u
# Should show only: Period: 300
```

### Deployment
```bash
# Deploy to production environment
./deploy.sh -e prod -r eu-west-1 --profile production

# Or deploy to staging first for testing
./deploy.sh -e staging -r eu-west-1 --profile staging
```

### Verification After Deployment
1. **Check CloudFormation Stack Status**:
   ```bash
   aws cloudformation describe-stacks --stack-name ecs-scaling-prod --query 'Stacks[0].StackStatus'
   ```

2. **Verify ScaleDownTriggerAlarm Creation**:
   ```bash
   aws cloudwatch describe-alarms --alarm-names "api-prod-ScaleDownTrigger" --region eu-west-1
   ```

3. **Confirm Alarm Configuration**:
   ```bash
   aws cloudwatch describe-alarms --alarm-names "api-prod-ScaleDownTrigger" \
     --query 'MetricAlarms[0].Metrics[*].MetricStat.Period' --region eu-west-1
   ```
   Should return: `[300, 300, 300, 300]`

## Monitoring and Testing

### Test Scenarios
1. **Low Resource Utilization**: Verify scale-down occurs when all conditions are met
2. **At Minimum Capacity**: Confirm no scale-down attempts when task count equals MinCapacity
3. **Mixed Conditions**: Test that scale-down only occurs when ALL conditions are satisfied

### Monitoring Points
- CloudWatch Alarms dashboard for alarm states
- ECS Service scaling activity logs
- CloudFormation stack events for successful updates

## Risk Assessment

### Low Risk Changes
- **Syntax Only**: No functional logic changes to scaling behavior
- **Conservative**: Longer period is safer than shorter for scaling decisions
- **Backward Compatible**: No breaking changes to existing functionality

### Mitigation Strategies
- **Gradual Rollout**: Deploy to staging environment first
- **Monitoring**: Watch scaling behavior closely after deployment
- **Quick Rollback**: Can revert period change if issues arise

## Rollback Plan

If issues occur with the 300-second period:

### Option 1: Revert to Individual Alarms
```bash
# Remove ScaleDownTriggerAlarm and use individual alarms
git checkout HEAD~2 template.yaml  # Before ScaleDownTriggerAlarm was added
./deploy.sh -e prod
```

### Option 2: Adjust Period Value
```bash
# Change all metrics to 60-second periods if faster response needed
# Edit template.yaml to change all Period: 300 to Period: 60 in ScaleDownTriggerAlarm
./deploy.sh -e prod
```

## Summary

This fix resolves the CloudWatch alarm creation error by ensuring all metrics in the ScaleDownTriggerAlarm have consistent period values. The change is minimal, safe, and maintains all existing scaling functionality while making the configuration compliant with AWS CloudWatch requirements.

**Key Benefits:**
- ✅ Resolves CloudFormation deployment failures
- ✅ Maintains existing scaling logic and safety measures  
- ✅ Improves metric correlation with consistent timing
- ✅ Follows AWS CloudWatch best practices