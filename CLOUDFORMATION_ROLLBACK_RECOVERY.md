# CloudFormation UPDATE_R<PERSON><PERSON><PERSON>CK_FAILED Recovery

## Issue Summary
The CloudFormation stack `ecs-scaling-prod` was stuck in `UPDATE_ROLL<PERSON>CK_FAILED` state, preventing any further updates.

**Error Message:**
```
Stack:arn:aws:cloudformation:eu-west-1:689621821214:stack/ecs-scaling-prod/eb763110-68bb-11f0-942e-0642f3c1f08f is in UPDATE_ROLLBACK_FAILED state and can not be updated.
```

## Root Cause Analysis
1. **Initial Problem**: The SNS subscription resource `ScalingNotificationSubscription` was missing (likely deleted manually or never confirmed)
2. **Update Failure**: CloudFormation tried to update the missing subscription and failed with "Subscription does not exist (Service: Sns, Status Code: 404)"
3. **Rollback Failure**: When CloudFormation attempted to rollback the failed update, it also failed because it couldn't rollback a resource that doesn't exist
4. **Stuck State**: The stack became stuck in `UPDATE_ROLLBACK_FAILED` state

## Recovery Process

### Step 1: Analyze Stack State
```bash
aws cloudformation describe-stacks --stack-name ecs-scaling-prod --region eu-west-1
```
- Confirmed stack status: `UPDATE_ROLLBACK_FAILED`
- Identified problematic resource: `ScalingNotificationSubscription`

### Step 2: Examine Stack Events
```bash
aws cloudformation describe-stack-events --stack-name ecs-scaling-prod --region eu-west-1
```
- Found the exact error: "Subscription does not exist (Service: Sns, Status Code: 404)"
- Confirmed both update and rollback failed on the same resource

### Step 3: Continue Update Rollback with Skip
```bash
aws cloudformation continue-update-rollback --stack-name ecs-scaling-prod --region eu-west-1 --resources-to-skip ScalingNotificationSubscription
```
- This command instructs CloudFormation to skip the problematic resource during rollback
- Successfully restored stack to `UPDATE_ROLLBACK_COMPLETE` state

### Step 4: Deploy Fixed Template
```bash
./deploy.sh -e prod -r eu-west-1
```
- Used the updated template with `ScalingNotificationSubscriptionV2` resource
- CloudFormation created a new subscription and deleted the old reference
- Stack successfully updated to `UPDATE_COMPLETE` state

## Template Changes Made
The template was updated to handle the missing SNS subscription:

```yaml
# Changed from ScalingNotificationSubscription to ScalingNotificationSubscriptionV2
ScalingNotificationSubscriptionV2:
  Type: AWS::SNS::Subscription
  Condition: HasNotificationEmail
  UpdateReplacePolicy: Delete
  DeletionPolicy: Delete
  Properties:
    TopicArn: !Ref ScalingNotificationTopic
    Protocol: email
    Endpoint: !Ref NotificationEmail
    # Force recreation of subscription to trigger new email confirmation
    DeliveryPolicy:
      healthyRetryPolicy:
        numRetries: 3
        minDelayTarget: 20
        maxDelayTarget: 20
```

## Key Changes:
1. **New Logical Name**: `ScalingNotificationSubscriptionV2` forces CloudFormation to create a new resource
2. **Deletion Policies**: Added `UpdateReplacePolicy: Delete` and `DeletionPolicy: Delete`
3. **DeliveryPolicy**: Added to ensure the resource has unique properties

## Resolution Outcome
✅ **Stack Status**: `UPDATE_COMPLETE`
✅ **New SNS Subscription**: Created successfully
✅ **Email Confirmation**: New confirmation email <NAME_EMAIL>
✅ **Stack Functionality**: All scaling policies and alarms operational

## Preventive Measures

### 1. SNS Subscription Management
- **Always confirm email subscriptions** immediately after deployment
- **Monitor subscription status** regularly
- **Use Infrastructure as Code** - avoid manual deletion of CloudFormation-managed resources

### 2. CloudFormation Best Practices
- **Test updates in staging** before applying to production
- **Use change sets** to preview changes before deployment
- **Monitor stack events** during updates
- **Implement proper deletion policies** for critical resources

### 3. Recovery Preparedness
- **Document recovery procedures** for common failure scenarios
- **Maintain backup templates** with alternative resource names
- **Use stack policies** to protect critical resources from accidental updates/deletions

### 4. Monitoring and Alerting
- Set up CloudWatch alarms for stack update failures
- Monitor SNS subscription confirmations
- Alert on stack drift detection

## Commands Reference

### Check Stack Status
```bash
aws cloudformation describe-stacks --stack-name STACK_NAME --region REGION --query 'Stacks[0].{Status:StackStatus,Reason:StackStatusReason}'
```

### Continue Failed Rollback
```bash
aws cloudformation continue-update-rollback --stack-name STACK_NAME --region REGION --resources-to-skip RESOURCE_NAME
```

### List Stack Events
```bash
aws cloudformation describe-stack-events --stack-name STACK_NAME --region REGION --max-items 20
```

## Date: 2025-07-27
## Status: ✅ RESOLVED