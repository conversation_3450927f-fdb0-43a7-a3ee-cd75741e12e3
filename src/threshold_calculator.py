import boto3
import json
import logging
import os
from typing import Dict, Any, Optional

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
ecs_client = boto3.client('ecs')
cloudwatch_client = boto3.client('cloudwatch')

def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Lambda function to dynamically calculate and update ALB request count thresholds
    based on current ECS service task count.
    
    Args:
        event: Lambda event containing cluster_name, service_name, and threshold parameters
        context: Lambda context object
        
    Returns:
        Dict containing status and calculated thresholds
    """
    try:
        # Extract parameters from event or environment variables
        cluster_name = event.get('cluster_name') or os.environ.get('CLUSTER_NAME')
        service_name = event.get('service_name') or os.environ.get('SERVICE_NAME')
        base_rpm_per_task = float(event.get('base_rpm_per_task', os.environ.get('BASE_RPM_PER_TASK', 300)))
        high_request_multiplier = float(event.get('high_request_multiplier', os.environ.get('HIGH_REQUEST_MULTIPLIER', 1.0)))
        low_request_multiplier = float(event.get('low_request_multiplier', os.environ.get('LOW_REQUEST_MULTIPLIER', 0.17)))
        
        if not cluster_name or not service_name:
            raise ValueError("cluster_name and service_name are required")
        
        logger.info(f"Processing threshold calculation for service: {service_name} in cluster: {cluster_name}")
        
        # Get current task count from ECS service
        current_task_count = get_current_task_count(cluster_name, service_name)
        
        if current_task_count == 0:
            logger.warning(f"Service {service_name} has 0 running tasks. Using minimum threshold values.")
            current_task_count = 1  # Use minimum to avoid division by zero
        
        # Calculate dynamic thresholds
        thresholds = calculate_thresholds(
            current_task_count, 
            base_rpm_per_task, 
            high_request_multiplier, 
            low_request_multiplier
        )
        
        # Update CloudWatch alarms with new thresholds
        update_results = update_cloudwatch_alarms(service_name, thresholds)
        
        # Prepare response
        response = {
            'statusCode': 200,
            'body': {
                'service_name': service_name,
                'cluster_name': cluster_name,
                'current_task_count': current_task_count,
                'calculated_thresholds': thresholds,
                'update_results': update_results,
                'timestamp': context.aws_request_id
            }
        }
        
        logger.info(f"Successfully updated thresholds: {json.dumps(thresholds)}")
        return response
        
    except Exception as e:
        logger.error(f"Error in threshold calculation: {str(e)}", exc_info=True)
        return {
            'statusCode': 500,
            'body': {
                'error': str(e),
                'timestamp': getattr(context, 'aws_request_id', 'unknown')
            }
        }

def get_current_task_count(cluster_name: str, service_name: str) -> int:
    """
    Get the current running task count for an ECS service.
    
    Args:
        cluster_name: Name of the ECS cluster
        service_name: Name of the ECS service
        
    Returns:
        Current running task count
    """
    try:
        response = ecs_client.describe_services(
            cluster=cluster_name,
            services=[service_name]
        )
        
        if not response['services']:
            raise ValueError(f"Service {service_name} not found in cluster {cluster_name}")
        
        service = response['services'][0]
        running_count = service['runningCount']
        desired_count = service['desiredCount']
        
        logger.info(f"Service {service_name}: running={running_count}, desired={desired_count}")
        
        # Use running count, but fall back to desired count if running is 0
        return running_count if running_count > 0 else max(desired_count, 1)
        
    except Exception as e:
        logger.error(f"Error getting task count: {str(e)}")
        raise

def calculate_thresholds(
    task_count: int, 
    base_rpm_per_task: float, 
    high_multiplier: float, 
    low_multiplier: float
) -> Dict[str, float]:
    """
    Calculate dynamic request count thresholds based on current task count.
    
    Args:
        task_count: Current number of running tasks
        base_rpm_per_task: Base requests per minute per task
        high_multiplier: Multiplier for high threshold calculation
        low_multiplier: Multiplier for low threshold calculation
        
    Returns:
        Dictionary containing calculated thresholds
    """
    # Calculate thresholds
    # High threshold: base_rpm_per_task * high_multiplier * task_count
    high_threshold = base_rpm_per_task * high_multiplier * task_count
    
    # Low threshold: base_rpm_per_task * low_multiplier * task_count
    low_threshold = base_rpm_per_task * low_multiplier * task_count
    
    # Convert to per-minute values (CloudWatch RequestCount is per minute)
    # The alarm period is 60 seconds for high threshold, so we use the RPM directly
    high_threshold_per_minute = high_threshold
    
    # The alarm period is 300 seconds (5 minutes) for low threshold
    # So we need to multiply by 5 to get the 5-minute total
    low_threshold_5_minutes = low_threshold * 5
    
    thresholds = {
        'high_threshold_rpm': high_threshold,
        'low_threshold_rpm': low_threshold,
        'high_threshold_cloudwatch': high_threshold_per_minute,
        'low_threshold_cloudwatch': low_threshold_5_minutes,
        'task_count': task_count,
        'base_rpm_per_task': base_rpm_per_task
    }
    
    logger.info(f"Calculated thresholds for {task_count} tasks: {thresholds}")
    return thresholds

def update_cloudwatch_alarms(service_name: str, thresholds: Dict[str, float]) -> Dict[str, Any]:
    """
    Update CloudWatch alarms with new threshold values.
    
    Args:
        service_name: Name of the ECS service
        thresholds: Dictionary containing calculated thresholds
        
    Returns:
        Dictionary containing update results
    """
    update_results = {}
    
    try:
        # Update high request count alarm
        high_alarm_name = f"{service_name}-HighRequestCount"
        high_result = update_alarm_threshold(
            high_alarm_name, 
            thresholds['high_threshold_cloudwatch']
        )
        update_results['high_alarm'] = high_result
        
        # Update low request count alarm
        low_alarm_name = f"{service_name}-LowRequestCount"
        low_result = update_alarm_threshold(
            low_alarm_name, 
            thresholds['low_threshold_cloudwatch']
        )
        update_results['low_alarm'] = low_result
        
        logger.info(f"Successfully updated alarms for service {service_name}")
        
    except Exception as e:
        logger.error(f"Error updating CloudWatch alarms: {str(e)}")
        update_results['error'] = str(e)
    
    return update_results

def update_alarm_threshold(alarm_name: str, new_threshold: float) -> Dict[str, Any]:
    """
    Update a specific CloudWatch alarm threshold.
    
    Args:
        alarm_name: Name of the CloudWatch alarm
        new_threshold: New threshold value
        
    Returns:
        Dictionary containing update result
    """
    try:
        # First, get the current alarm configuration
        response = cloudwatch_client.describe_alarms(AlarmNames=[alarm_name])
        
        if not response['MetricAlarms']:
            logger.warning(f"Alarm {alarm_name} not found")
            return {'status': 'not_found', 'alarm_name': alarm_name}
        
        alarm = response['MetricAlarms'][0]
        current_threshold = alarm['Threshold']
        
        # Only update if threshold has changed significantly (avoid unnecessary updates)
        threshold_change_percent = abs(new_threshold - current_threshold) / max(current_threshold, 1) * 100
        
        if threshold_change_percent < 5:  # Less than 5% change
            logger.info(f"Threshold change for {alarm_name} is minimal ({threshold_change_percent:.1f}%), skipping update")
            return {
                'status': 'skipped',
                'alarm_name': alarm_name,
                'current_threshold': current_threshold,
                'new_threshold': new_threshold,
                'change_percent': threshold_change_percent
            }
        
        # Update the alarm with new threshold
        cloudwatch_client.put_metric_alarm(
            AlarmName=alarm['AlarmName'],
            AlarmDescription=alarm['AlarmDescription'],
            ActionsEnabled=alarm['ActionsEnabled'],
            OKActions=alarm.get('OKActions', []),
            AlarmActions=alarm.get('AlarmActions', []),
            InsufficientDataActions=alarm.get('InsufficientDataActions', []),
            MetricName=alarm['MetricName'],
            Namespace=alarm['Namespace'],
            Statistic=alarm.get('Statistic'),
            ExtendedStatistic=alarm.get('ExtendedStatistic'),
            Dimensions=alarm.get('Dimensions', []),
            Period=alarm['Period'],
            Unit=alarm.get('Unit'),
            EvaluationPeriods=alarm['EvaluationPeriods'],
            DatapointsToAlarm=alarm.get('DatapointsToAlarm'),
            Threshold=new_threshold,
            ComparisonOperator=alarm['ComparisonOperator'],
            TreatMissingData=alarm.get('TreatMissingData', 'missing')
        )
        
        logger.info(f"Updated alarm {alarm_name}: {current_threshold} -> {new_threshold}")
        
        return {
            'status': 'updated',
            'alarm_name': alarm_name,
            'previous_threshold': current_threshold,
            'new_threshold': new_threshold,
            'change_percent': threshold_change_percent
        }
        
    except Exception as e:
        logger.error(f"Error updating alarm {alarm_name}: {str(e)}")
        return {
            'status': 'error',
            'alarm_name': alarm_name,
            'error': str(e)
        }

def validate_parameters(event: Dict[str, Any]) -> None:
    """
    Validate input parameters.
    
    Args:
        event: Lambda event containing parameters
        
    Raises:
        ValueError: If parameters are invalid
    """
    required_params = ['cluster_name', 'service_name']
    
    for param in required_params:
        if not event.get(param) and not os.environ.get(param.upper()):
            raise ValueError(f"Missing required parameter: {param}")
    
    # Validate numeric parameters
    numeric_params = ['base_rpm_per_task', 'high_request_multiplier', 'low_request_multiplier']
    
    for param in numeric_params:
        value = event.get(param) or os.environ.get(param.upper())
        if value is not None:
            try:
                float_value = float(value)
                if float_value <= 0:
                    raise ValueError(f"Parameter {param} must be positive")
            except (ValueError, TypeError):
                raise ValueError(f"Parameter {param} must be a valid number")