# ECS Multi-Metric Step Scaling SAM Application

A comprehensive SAM (Serverless Application Model) application that implements intelligent multi-metric step scaling for existing Amazon ECS services running on AWS Fargate. This solution is specifically optimized for data-intensive applications and provides scaling based on CPU Utilization, Memory Utilization, and ALB Request Count.

## 🏗️ Architecture Overview

This application creates a complete auto-scaling solution with the following components:

### Core Components
- **Application Auto Scaling Target**: Registers your existing ECS service for scaling
- **Step Scaling Policies**: Separate policies for CPU, Memory, and ALB Request Count scaling
- **CloudWatch Alarms**: Individual and composite alarms for intelligent scaling decisions
- **Lambda Function**: Dynamic threshold calculation for ALB request count based on current task count
- **CloudWatch Dashboard**: Comprehensive monitoring and visualization
- **SNS Notifications**: Alerts for scaling events

### Scaling Strategy
- **Scale-Up Triggers**: ANY metric (CPU, Memory, or ALB Request Count) exceeds threshold
- **Scale-Down Triggers**: ALL metrics must be below threshold (conservative approach)
- **Emergency Scaling**: Rapid scaling when multiple metrics are critically high
- **Dynamic Thresholds**: ALB request count thresholds automatically adjust based on current task count

## 📋 Prerequisites

Before deploying this application, ensure you have:

1. **AWS CLI** installed and configured
2. **SAM CLI** installed (version 1.50.0 or later)
3. **Python 3.9** or later
4. **Existing ECS Infrastructure**:
   - ECS Cluster (Fargate-enabled)
   - ECS Service running on the cluster
   - Application Load Balancer integrated with the service
   - Target Group associated with the ALB

## 🚀 Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd step-scale-aws-ecs
```

### 2. Update Parameters

Edit the parameter file for your environment (`parameters/dev.json`, `parameters/staging.json`, or `parameters/prod.json`):

```json
[
  {
    "ParameterKey": "ExistingClusterName",
    "ParameterValue": "your-actual-cluster-name"
  },
  {
    "ParameterKey": "ExistingServiceName", 
    "ParameterValue": "your-actual-service-name"
  },
  {
    "ParameterKey": "LoadBalancerFullName",
    "ParameterValue": "app/your-alb-name/1234567890abcdef"
  },
  {
    "ParameterKey": "TargetGroupFullName",
    "ParameterValue": "targetgroup/your-tg-name/1234567890abcdef"
  }
]
```

### 3. Deploy

```bash
# Deploy to development environment
./deploy.sh -e dev -r us-east-1

# Deploy to production with specific profile
./deploy.sh -e prod -r us-west-2 --profile production

# Validate template only
./deploy.sh --validate-only
```

## 📊 Configuration Parameters

### Environment-Specific Settings

| Parameter | Dev | Staging | Prod | Description |
|-----------|-----|---------|------|-------------|
| MinCapacity | 2 | 3 | 3 | Minimum number of tasks |
| MaxCapacity | 8 | 12 | 15 | Maximum number of tasks |
| CPUScaleUpThreshold | 60% | 55% | 50% | CPU threshold for scaling up |
| CPUScaleDownThreshold | 40% | 35% | 35% | CPU threshold for scaling down |
| MemoryScaleUpThreshold | 60% | 55% | 50% | Memory threshold for scaling up |
| MemoryScaleDownThreshold | 40% | 35% | 35% | Memory threshold for scaling down |
| BaseRPMPerTask | 200 | 250 | 300 | Base requests per minute per task |
| ScaleUpCooldown | 5min | 7.5min | 10min | Cooldown after scaling up |
| ScaleDownCooldown | 10min | 12.5min | 15min | Cooldown after scaling down |

### ALB Request Count Thresholds

The application automatically calculates dynamic thresholds based on:
- **Current Task Count**: Retrieved from ECS service
- **Base RPM per Task**: Configurable baseline capacity
- **Multipliers**: High (1.0) and Low (0.17) request multipliers

**Example Calculation for 5 tasks:**
- Scale-up threshold: 300 RPM/task × 1.0 × 5 tasks = 1500 RPM
- Scale-down threshold: 300 RPM/task × 0.17 × 5 tasks = 255 RPM

## 🔧 Deployment Options

### Basic Deployment
```bash
./deploy.sh -e dev
```

### Advanced Deployment
```bash
./deploy.sh \
  --environment prod \
  --region us-west-2 \
  --stack-name my-custom-scaling-stack \
  --parameter-file custom-parameters.json \
  --profile production
```

### Validation Only
```bash
./deploy.sh --validate-only
```

### Dry Run
```bash
./deploy.sh --dry-run -e prod
```

## 📈 Monitoring and Observability

### CloudWatch Dashboard

The application creates a comprehensive dashboard with:
- **Multi-Metric Overview**: CPU, Memory, and Request Count trends
- **Task Count and Scaling Activities**: Current capacity and scaling events
- **ALB Performance Metrics**: Response times and HTTP status codes
- **Application Log Activity**: Log volume indicators

### Alarms and Notifications

- **Individual Alarms**: Separate alarms for each metric
- **Composite Alarms**: Intelligent scaling logic
- **Emergency Alarms**: Critical situation detection
- **SNS Notifications**: Email alerts for scaling events

### Key Metrics to Monitor

1. **ECS Service Metrics**:
   - CPUUtilization
   - MemoryUtilization
   - RunningTaskCount

2. **ALB Metrics**:
   - RequestCount
   - TargetResponseTime
   - HTTPCode_Target_2XX_Count
   - HTTPCode_Target_5XX_Count

3. **Scaling Metrics**:
   - ScalingActivities
   - Alarm states
   - Lambda function execution

## 🔍 Troubleshooting

### Common Issues

#### 1. Scaling Not Triggered
**Symptoms**: Metrics exceed thresholds but no scaling occurs

**Solutions**:
- Verify Application Auto Scaling service-linked role exists
- Check CloudWatch alarm states in the console
- Ensure ECS service has scaling permissions
- Verify alarm actions are configured correctly

```bash
# Check service-linked role
aws iam get-role --role-name AWSServiceRoleForApplicationAutoScaling_ECSService

# Check alarm states
aws cloudwatch describe-alarms --alarm-names "YourService-HighCPUUtilization"
```

#### 2. Lambda Function Errors
**Symptoms**: ALB request count thresholds not updating

**Solutions**:
- Check Lambda function logs in CloudWatch
- Verify IAM permissions for ECS and CloudWatch access
- Ensure ECS service name and cluster name are correct

```bash
# Check Lambda logs
aws logs describe-log-groups --log-group-name-prefix "/aws/lambda/YourService-RequestThresholdCalculator"

# Test Lambda function
aws lambda invoke --function-name YourService-RequestThresholdCalculator --payload '{"cluster_name":"your-cluster","service_name":"your-service"}' response.json
```

#### 3. Template Validation Errors
**Symptoms**: SAM template validation fails

**Solutions**:
- Validate JSON syntax in parameter files
- Check CloudFormation template syntax
- Verify all required parameters are provided

```bash
# Validate template
sam validate --region us-east-1

# Validate parameter file
python3 -m json.tool parameters/dev.json
```

#### 4. Deployment Failures
**Symptoms**: CloudFormation stack creation/update fails

**Solutions**:
- Check CloudFormation events in AWS Console
- Verify IAM permissions for stack operations
- Ensure resource names don't conflict with existing resources

```bash
# Check stack events
aws cloudformation describe-stack-events --stack-name ecs-scaling-dev

# Check stack status
aws cloudformation describe-stacks --stack-name ecs-scaling-dev
```

#### 5. Parameter Format Issues
**Symptoms**: SAM deploy fails with "Invalid value for '--parameter-overrides'" error

**Solutions**:
- The deploy script automatically converts JSON parameter files to SAM CLI format
- Ensure parameter files are valid JSON
- Check that all required parameters are present in the parameter file

```bash
# Test parameter file conversion
python3 -c "
import json
with open('parameters/staging.json', 'r') as f:
    params = json.load(f)
    for param in params:
        print(f\"{param['ParameterKey']}={param['ParameterValue']}\")
"

# Validate parameter file JSON syntax
python3 -m json.tool parameters/staging.json
```

#### 6. CloudFormation Stack in ROLLBACK_COMPLETE State
**Symptoms**: Error message "Stack is in ROLLBACK_COMPLETE state and can not be updated"

**Solutions**:
- Use the `--force-delete` flag to automatically delete and recreate failed stacks
- Manually delete the stack and redeploy
- Use a different stack name

```bash
# Automatic stack recovery
./deploy.sh -e staging --force-delete

# Manual stack deletion
aws cloudformation delete-stack --stack-name ecs-scaling-staging --region eu-west-1
aws cloudformation wait stack-delete-complete --stack-name ecs-scaling-staging --region eu-west-1
```

#### 7. Resource Conflict with Existing Scaling Configuration
**Symptoms**: Error message "service/cluster/service|ecs:service:DesiredCount|ecs already exists in stack"

**Solutions**:
This occurs when the ECS service already has Application Auto Scaling configured in another CloudFormation stack.

**Option 1: Remove existing scaling configuration**
```bash
# Find existing scaling target
aws application-autoscaling describe-scalable-targets --service-namespace ecs

# Remove existing scaling target
aws application-autoscaling deregister-scalable-target \
  --service-namespace ecs \
  --resource-id "service/Backend-API/api-staging" \
  --scalable-dimension ecs:service:DesiredCount
```

**Option 2: Use a different stack name**
```bash
./deploy.sh -e staging -s ecs-scaling-staging-v2
```

**Option 3: Import existing resources**
- Export the existing scaling configuration from the other stack
- Import it into your new stack using CloudFormation import operations

### Debugging Steps

1. **Enable Detailed Monitoring**:
   ```bash
   # Enable detailed monitoring for ECS service
   aws ecs update-service --cluster your-cluster --service your-service --enable-execute-command
   ```

2. **Check Application Auto Scaling**:
   ```bash
   # List scalable targets
   aws application-autoscaling describe-scalable-targets --service-namespace ecs
   
   # List scaling policies
   aws application-autoscaling describe-scaling-policies --service-namespace ecs
   ```

3. **Monitor Scaling Activities**:
   ```bash
   # View scaling activities
   aws application-autoscaling describe-scaling-activities --service-namespace ecs --resource-id "service/your-cluster/your-service"
   ```

### Performance Tuning

#### Threshold Optimization
- **Too Aggressive**: Reduce thresholds (45% instead of 50%)
- **Too Conservative**: Increase thresholds (60% instead of 50%)
- **Frequent Scaling**: Increase cooldown periods
- **Slow Response**: Decrease cooldown periods

#### ALB Request Count Tuning
- **Monitor baseline**: Analyze 2-4 weeks of request patterns
- **Adjust BaseRPMPerTask**: Based on actual application capacity
- **Fine-tune multipliers**: Based on traffic patterns

## 🔒 Security Considerations

### IAM Permissions
The application creates minimal IAM roles with least-privilege access:
- **Lambda Execution Role**: ECS describe and CloudWatch alarm management
- **Application Auto Scaling Role**: Service-linked role for ECS scaling

### Network Security
- Lambda function runs in AWS managed VPC
- No custom VPC configuration required
- All communications use AWS service endpoints

### Data Protection
- No sensitive data stored in Lambda function
- CloudWatch logs encrypted at rest
- SNS topics support encryption in transit

## 🚀 Advanced Usage

### Custom Metrics Integration
To add custom application metrics:

1. **Create Custom CloudWatch Metrics**:
   ```python
   import boto3
   cloudwatch = boto3.client('cloudwatch')
   
   cloudwatch.put_metric_data(
       Namespace='Custom/Application',
       MetricData=[
           {
               'MetricName': 'ProcessingQueueDepth',
               'Value': queue_depth,
               'Unit': 'Count'
           }
       ]
   )
   ```

2. **Add Custom Alarms** to template.yaml
3. **Update Composite Alarms** to include custom metrics

### Multi-Region Deployment
Deploy to multiple regions:

```bash
# Deploy to multiple regions
for region in us-east-1 us-west-2 eu-west-1; do
  ./deploy.sh -e prod -r $region --stack-name ecs-scaling-prod-$region
done
```

### Blue-Green Deployment Integration
For zero-downtime updates:

1. Deploy scaling to both blue and green environments
2. Update service names in parameters during cutover
3. Monitor scaling behavior during transition

## 📚 Additional Resources

- [AWS Application Auto Scaling Documentation](https://docs.aws.amazon.com/autoscaling/application/userguide/)
- [ECS Service Auto Scaling](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/service-auto-scaling.html)
- [CloudWatch Composite Alarms](https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/Create_Composite_Alarm.html)
- [SAM CLI Reference](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-command-reference.html)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review CloudWatch logs and metrics
3. Create an issue in the repository
4. Contact your AWS support team for infrastructure-related issues

---

**Note**: This application is designed for existing ECS services. Ensure your ECS cluster, service, and ALB are properly configured before deployment.