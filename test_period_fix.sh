#!/bin/bash

# Test script to verify the period fix for ScaleDownTriggerAlarm
# This script performs a dry-run deployment to check if the alarm can be created

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Testing ScaleDownTriggerAlarm period fix..."

# Validate template first
print_status "Validating CloudFormation template..."
if aws cloudformation validate-template --template-body file://template.yaml > /dev/null 2>&1; then
    print_success "Template validation passed"
else
    print_error "Template validation failed"
    exit 1
fi

# Check if all metrics in ScaleDownTriggerAlarm have the same period
print_status "Checking metric periods in ScaleDownTriggerAlarm..."

# Extract period values from the template
PERIODS=$(grep -A 20 "ScaleDownTriggerAlarm:" template.yaml | grep "Period:" | awk '{print $2}' | sort -u)
PERIOD_COUNT=$(echo "$PERIODS" | wc -l)

if [ "$PERIOD_COUNT" -eq 1 ]; then
    PERIOD_VALUE=$(echo "$PERIODS" | head -1)
    print_success "All metrics have the same period: $PERIOD_VALUE seconds"
else
    print_error "Metrics have different periods:"
    echo "$PERIODS"
    exit 1
fi

# Perform a dry-run deployment to staging to test the fix
print_status "Performing dry-run deployment to test alarm creation..."

# Use staging environment for testing
STACK_NAME="ecs-scaling-period-test"
PARAMETER_FILE="parameters/staging.json"

# Check if parameter file exists
if [ ! -f "$PARAMETER_FILE" ]; then
    print_error "Parameter file $PARAMETER_FILE not found"
    exit 1
fi

# Create changeset to test without actually deploying
print_status "Creating changeset to validate alarm configuration..."

aws cloudformation create-change-set \
    --stack-name "$STACK_NAME" \
    --template-body file://template.yaml \
    --parameters file://"$PARAMETER_FILE" \
    --capabilities CAPABILITY_NAMED_IAM CAPABILITY_AUTO_EXPAND \
    --change-set-name "period-fix-test-$(date +%s)" \
    --region eu-west-1 > /dev/null 2>&1

if [ $? -eq 0 ]; then
    print_success "Changeset created successfully - alarm configuration is valid"
    
    # Clean up the changeset
    print_status "Cleaning up test changeset..."
    CHANGESET_NAME=$(aws cloudformation list-change-sets --stack-name "$STACK_NAME" --region eu-west-1 --query 'Summaries[0].ChangeSetName' --output text 2>/dev/null || echo "")
    
    if [ "$CHANGESET_NAME" != "" ] && [ "$CHANGESET_NAME" != "None" ]; then
        aws cloudformation delete-change-set --stack-name "$STACK_NAME" --change-set-name "$CHANGESET_NAME" --region eu-west-1 > /dev/null 2>&1
        print_success "Test changeset cleaned up"
    fi
else
    print_error "Changeset creation failed - there may still be issues with the alarm configuration"
    exit 1
fi

print_success "Period fix validation completed successfully!"
print_status "The ScaleDownTriggerAlarm should now deploy without period mismatch errors."