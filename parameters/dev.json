[{"ParameterKey": "ExistingClusterName", "ParameterValue": "my-dev-cluster"}, {"ParameterKey": "ExistingServiceName", "ParameterValue": "my-dev-service"}, {"ParameterKey": "LoadBalancerFullName", "ParameterValue": "app/my-dev-alb/1234567890abcdef"}, {"ParameterKey": "TargetGroupFullName", "ParameterValue": "targetgroup/my-dev-tg/1234567890abcdef"}, {"ParameterKey": "MinCapacity", "ParameterValue": "2"}, {"ParameterKey": "MaxCapacity", "ParameterValue": "8"}, {"ParameterKey": "CPUScaleUpThreshold", "ParameterValue": "55"}, {"ParameterKey": "CPUScaleDownThreshold", "ParameterValue": "40"}, {"ParameterKey": "MemoryScaleUpThreshold", "ParameterValue": "55"}, {"ParameterKey": "MemoryScaleDownThreshold", "ParameterValue": "40"}, {"ParameterKey": "BaseRPMPerTask", "ParameterValue": "1190"}, {"ParameterKey": "HighRequestCountMultiplier", "ParameterValue": "1.2"}, {"ParameterKey": "LowRequestCountMultiplier", "ParameterValue": "0.2"}, {"ParameterKey": "CPUMemoryScaleUpCooldown", "ParameterValue": "60"}, {"ParameterKey": "ALBScaleUpCooldown", "ParameterValue": "60"}, {"ParameterKey": "ScaleDownCooldown", "ParameterValue": "600"}, {"ParameterKey": "NotificationEmail", "ParameterValue": "<EMAIL>"}, {"ParameterKey": "Environment", "ParameterValue": "dev"}]