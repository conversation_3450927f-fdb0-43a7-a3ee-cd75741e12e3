[{"ParameterKey": "ExistingClusterName", "ParameterValue": "Backend-API"}, {"ParameterKey": "ExistingServiceName", "ParameterValue": "api-prod"}, {"ParameterKey": "LoadBalancerFullName", "ParameterValue": "app/ApiLoadbalancer/c497b69ace2aae43"}, {"ParameterKey": "TargetGroupFullName", "ParameterValue": "targetgroup/prod-ecs-target-group/af37138a7ca9b745"}, {"ParameterKey": "MinCapacity", "ParameterValue": "5"}, {"ParameterKey": "MaxCapacity", "ParameterValue": "50"}, {"ParameterKey": "CPUScaleUpThreshold", "ParameterValue": "55"}, {"ParameterKey": "CPUScaleDownThreshold", "ParameterValue": "35"}, {"ParameterKey": "MemoryScaleUpThreshold", "ParameterValue": "55"}, {"ParameterKey": "MemoryScaleDownThreshold", "ParameterValue": "35"}, {"ParameterKey": "BaseRPMPerTask", "ParameterValue": "1190"}, {"ParameterKey": "HighRequestCountMultiplier", "ParameterValue": "1.0"}, {"ParameterKey": "LowRequestCountMultiplier", "ParameterValue": "0.17"}, {"ParameterKey": "CPUMemoryScaleUpCooldown", "ParameterValue": "60"}, {"ParameterKey": "ALBScaleUpCooldown", "ParameterValue": "60"}, {"ParameterKey": "ScaleDownCooldown", "ParameterValue": "750"}, {"ParameterKey": "NotificationEmail", "ParameterValue": "<EMAIL>"}, {"ParameterKey": "Environment", "ParameterValue": "prod"}, {"ParameterKey": "CreateScalableTarget", "ParameterValue": "false"}, {"ParameterKey": "ExistingScalableTargetId", "ParameterValue": "service/Backend-API/api-prod|ecs:service:DesiredCount|ecs"}]