[{"ParameterKey": "ExistingClusterName", "ParameterValue": "Backend-API"}, {"ParameterKey": "ExistingServiceName", "ParameterValue": "api-staging"}, {"ParameterKey": "LoadBalancerFullName", "ParameterValue": "app/ApiLoadbalancer/c497b69ace2aae43"}, {"ParameterKey": "TargetGroupFullName", "ParameterValue": "targetgroup/staging-ecs-3030-target-group/314f7b2551e2592b"}, {"ParameterKey": "MinCapacity", "ParameterValue": "3"}, {"ParameterKey": "MaxCapacity", "ParameterValue": "12"}, {"ParameterKey": "CPUScaleUpThreshold", "ParameterValue": "55"}, {"ParameterKey": "CPUScaleDownThreshold", "ParameterValue": "35"}, {"ParameterKey": "MemoryScaleUpThreshold", "ParameterValue": "55"}, {"ParameterKey": "MemoryScaleDownThreshold", "ParameterValue": "35"}, {"ParameterKey": "BaseRPMPerTask", "ParameterValue": "1190"}, {"ParameterKey": "HighRequestCountMultiplier", "ParameterValue": "1.1"}, {"ParameterKey": "LowRequestCountMultiplier", "ParameterValue": "0.18"}, {"ParameterKey": "CPUMemoryScaleUpCooldown", "ParameterValue": "60"}, {"ParameterKey": "ALBScaleUpCooldown", "ParameterValue": "60"}, {"ParameterKey": "ScaleDownCooldown", "ParameterValue": "750"}, {"ParameterKey": "NotificationEmail", "ParameterValue": "<EMAIL>"}, {"ParameterKey": "Environment", "ParameterValue": "staging"}, {"ParameterKey": "CreateScalableTarget", "ParameterValue": "false"}, {"ParameterKey": "ExistingScalableTargetId", "ParameterValue": "service/Backend-API/api-staging|ecs:service:DesiredCount|ecs"}]