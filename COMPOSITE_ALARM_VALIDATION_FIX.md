# Composite Alarm ValidationException Fix

## Issue Description
CloudFormation deployment failed with the following error:
```
UPDATE_FAILED                AWS::CloudWatch::Composite   CompositeScaleDownAlarm      Resource handler returned  
                             Alarm                                                     message: "[Auto Scaling]   
                                                                                       action type(s) not         
                                                                                       supported for Composite    
                                                                                       Alarms (Service:           
                                                                                       AmazonCloudWatch; Status   
                                                                                       Code: 400; Error Code:     
                                                                                       ValidationException;       
                                                                                       Request ID: f1a06157-b301- 
                                                                                       410c-a234-464531343bfa;    
                                                                                       Proxy: null)"              
```

## Root Cause
AWS CloudWatch Composite Alarms cannot directly trigger Application Auto Scaling policies. The `CompositeScaleDownAlarm` was configured with `!Ref ScaleDownPolicy` in its `AlarmActions`, which is not supported. Composite Alarms can only trigger:
- SNS topics
- Lambda functions
- Systems Manager actions
- EC2 actions

But NOT Application Auto Scaling policies.

## Solution Implemented

### 1. Modified CompositeScaleDownAlarm
- **Removed**: `!Ref ScaleDownPolicy` from AlarmActions
- **Kept**: `!Ref ScalingNotificationTopic` for notifications
- **Result**: Composite alarm now only handles notifications and monitoring

### 2. Created ScaleDownTriggerAlarm
- **Type**: Individual CloudWatch Alarm with metric math
- **Purpose**: Triggers the ScaleDownPolicy when all conditions are met
- **Logic**: Uses metric math expression to evaluate:
  - CPU < CPUScaleDownThreshold (35%)
  - Memory < MemoryScaleDownThreshold (35%) 
  - Request Count < 50
  - Running Task Count > MinCapacity (5)

### 3. Metric Math Expression
```yaml
Expression: !Sub "IF(m1 < ${CPUScaleDownThreshold} AND m2 < ${MemoryScaleDownThreshold} AND m3 < 50 AND m4 > ${MinCapacity}, 0, 1)"
```
- Returns 0 when all scale-down conditions are met (triggers alarm)
- Returns 1 when conditions are not met (alarm stays OK)
- Threshold set to 1 with LessThanThreshold comparison

## Changes Made

### template.yaml
1. **CompositeScaleDownAlarm** (lines 437-445):
   - Removed `!Ref ScaleDownPolicy` from AlarmActions
   - Kept only SNS notification

2. **Added ScaleDownTriggerAlarm** (lines 447-512):
   - Individual alarm using metric math
   - Monitors 4 metrics: CPU, Memory, RequestCount, RunningTaskCount
   - Uses IF expression to combine conditions
   - Triggers ScaleDownPolicy when conditions are met

## Benefits of This Approach

### 1. AWS Compliance
- Follows AWS CloudWatch limitations
- No more ValidationException errors
- Uses supported alarm action types

### 2. Maintains Logic
- Same scaling conditions as before
- All four conditions must be met for scale-down
- Prevents scaling when at minimum capacity

### 3. Better Monitoring
- Composite alarm provides high-level status
- Individual trigger alarm handles actual scaling
- Both alarms visible in CloudWatch console

## Deployment Instructions

### Validation
```bash
# Validate template syntax
aws cloudformation validate-template --template-body file://template.yaml
```

### Deploy
```bash
# Deploy to production
./deploy.sh -e prod -r eu-west-1 --profile production
```

### Verification
1. **Check CloudFormation Stack**:
   ```bash
   aws cloudformation describe-stacks --stack-name ecs-scaling-prod
   ```

2. **Verify Alarms**:
   ```bash
   aws cloudwatch describe-alarms --alarm-names "api-prod-CompositeScaleDown" "api-prod-ScaleDownTrigger"
   ```

3. **Monitor Scaling**:
   - ECS Console → Backend-API → api-prod → Scaling tab
   - Verify no more ValidationException errors
   - Confirm scaling works when conditions are met

## Expected Behavior

### Before Fix
- CloudFormation deployment fails with ValidationException
- CompositeScaleDownAlarm cannot be created/updated

### After Fix
- CloudFormation deployment succeeds
- CompositeScaleDownAlarm provides monitoring and notifications
- ScaleDownTriggerAlarm handles actual scaling policy execution
- Scale-down occurs only when all conditions are met

## Risk Assessment
- **Low Risk**: Only affects alarm configuration, not scaling logic
- **Backward Compatible**: Same scaling behavior maintained
- **Fail-Safe**: If metric math fails, no unwanted scaling occurs

## Rollback Plan
If issues occur, revert to previous individual alarm approach:
```bash
git checkout HEAD~1 template.yaml
./deploy.sh -e prod
```