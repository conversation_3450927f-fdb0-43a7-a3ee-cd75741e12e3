# CPUScaleUpPolicy Update Summary

## Issue Requirements
Update the CPUScaleUpPolicy to conform to these new thresholds:
- When CPU Utilization is 55-65%: add 2 units
- When CPU Utilization is 65-70%: add 4 units  
- When CPU Utilization is 70%+: add 6 units
- Update cooldown period to 60 seconds

## Changes Made

### 1. Updated CPUScaleUpThreshold Parameter (template.yaml)
- Changed default value from 50% to 55%
- Updated description to reflect the new proactive threshold

### 2. Updated CPUMemoryScaleUpCooldown Parameter (template.yaml)
- Changed default value from 600 seconds (10 minutes) to 60 seconds (1 minute)
- Updated description to reflect "1 minute" instead of "10 minutes"

### 3. Updated CPUScaleUpPolicy Step Adjustments (template.yaml)
**Before:**
```yaml
StepAdjustments:
  - MetricIntervalLowerBound: 0
    MetricIntervalUpperBound: 10
    ScalingAdjustment: 1    # 50-60% CPU: +1 task
  - MetricIntervalLowerBound: 10
    MetricIntervalUpperBound: 25
    ScalingAdjustment: 2    # 60-75% CPU: +2 tasks
  - MetricIntervalLowerBound: 25
    ScalingAdjustment: 3    # 75%+ CPU: +3 tasks
```

**After:**
```yaml
StepAdjustments:
  - MetricIntervalLowerBound: 0
    MetricIntervalUpperBound: 10
    ScalingAdjustment: 2    # 55-65% CPU: +2 tasks
  - MetricIntervalLowerBound: 10
    MetricIntervalUpperBound: 15
    ScalingAdjustment: 4    # 65-70% CPU: +4 tasks
  - MetricIntervalLowerBound: 15
    ScalingAdjustment: 6    # 70%+ CPU: +6 tasks
```

### 4. Updated Parameter Files
Updated CPUScaleUpThreshold and CPUMemoryScaleUpCooldown values in all environment parameter files:

**dev.json:**
- CPUScaleUpThreshold: Changed from "60" to "55"
- CPUMemoryScaleUpCooldown: Changed from "300" to "60"

**staging.json:**
- CPUScaleUpThreshold: Already set to "55" (no change needed)
- CPUMemoryScaleUpCooldown: Changed from "450" to "60"

**prod.json:**
- CPUScaleUpThreshold: Changed from "50" to "55"  
- CPUMemoryScaleUpCooldown: Changed from "600" to "60"

## How It Works
1. The HighCPUAlarm uses CPUScaleUpThreshold (55%) as its threshold
2. When CPU utilization exceeds 55%, the alarm triggers the CPUScaleUpPolicy
3. The step scaling policy then applies the appropriate scaling based on how far above the threshold the metric is:
   - 55-65% CPU (0-10 above threshold): +2 tasks
   - 65-70% CPU (10-15 above threshold): +4 tasks
   - 70%+ CPU (15+ above threshold): +6 tasks
4. The policy uses a 60-second cooldown period to prevent rapid scaling events

## Verification
All changes have been implemented to match the exact requirements specified in the issue description:
- ✅ CPU Utilization 55-65%: add 2 units
- ✅ CPU Utilization 65-70%: add 4 units
- ✅ CPU Utilization 70%+: add 6 units  
- ✅ Cooldown period updated to 60 seconds