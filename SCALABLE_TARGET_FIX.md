# ScalableTarget Conflict Resolution

## Issue Description
When deploying to staging, the following error occurred:
```
CREATE_FAILED AWS::ApplicationAutoScaling::ScalableTarget ScalableTarget 
service/Backend-API/api-staging|ecs:service:DesiredCount|ecs already exists in stack
```

This error indicates that a ScalableTarget for the ECS service `service/Backend-API/api-staging` already exists in AWS, and CloudFormation cannot create a duplicate resource.

## Root Cause
AWS Application Auto Scaling only allows one ScalableTarget per ECS service resource. The error occurs when:
1. An existing ScalableTarget was created manually or by another CloudFormation stack
2. A previous deployment created the ScalableTarget but the stack was deleted without cleaning up the resource
3. Multiple CloudFormation stacks are trying to manage the same ECS service's auto-scaling

## Solution Implemented

### 1. Template Changes
Added conditional logic to the CloudFormation template (`template.yaml`) to handle existing ScalableTargets:

#### New Parameters:
- `CreateScalableTarget`: Boolean parameter to control whether to create a new ScalableTarget
- `ExistingScalableTargetId`: ARN of an existing ScalableTarget to use when `CreateScalableTarget` is false

#### New Condition:
- `ShouldCreateScalableTarget`: Evaluates to true when `CreateScalableTarget` parameter is "true"

#### Modified Resources:
- `ScalableTarget`: Made conditional using `ShouldCreateScalableTarget` condition
- All scaling policies: Updated to use conditional logic for `ScalingTargetId` property
- `ScalableTargetId` output: Made conditional to prevent unresolved resource dependencies

### 2. Parameter File Updates
Updated `parameters/staging.json` to:
- Set `CreateScalableTarget` to `false` for staging environment
- Added placeholder for `ExistingScalableTargetId` parameter

### 3. Helper Script
Created `find-scalable-target.sh` to help identify existing ScalableTarget ARNs.

## Usage Instructions

### For Environments with Existing ScalableTargets

1. **Find the existing ScalableTarget ARN:**
   ```bash
   ./find-scalable-target.sh Backend-API api-staging
   ```

2. **Update the parameter file:**
   Replace `REPLACE_WITH_EXISTING_SCALABLE_TARGET_ARN` in your parameter file with the actual ARN.
   
   The relevant parameters should look like this:
   ```json
   [
     {
       "ParameterKey": "CreateScalableTarget",
       "ParameterValue": "false"
     },
     {
       "ParameterKey": "ExistingScalableTargetId",
       "ParameterValue": "arn:aws:application-autoscaling:eu-west-1:123456789012:scalable-target/example"
     }
   ]
   ```

3. **Deploy normally:**
   ```bash
   ./deploy.sh -e staging
   ```

### For New Environments (No Existing ScalableTarget)

1. **Set parameters for new ScalableTarget:**
   ```json
   {
     "ParameterKey": "CreateScalableTarget",
     "ParameterValue": "true"
   }
   ```
   (No need to set `ExistingScalableTargetId`)

2. **Deploy normally:**
   ```bash
   ./deploy.sh -e staging
   ```

## Files Modified

1. **template.yaml**:
   - Added `CreateScalableTarget` and `ExistingScalableTargetId` parameters
   - Added `ShouldCreateScalableTarget` condition
   - Made `ScalableTarget` resource conditional
   - Updated all scaling policies to use conditional ScalableTarget references

2. **parameters/staging.json**:
   - Added `CreateScalableTarget: false`
   - Added placeholder for `ExistingScalableTargetId`

3. **find-scalable-target.sh** (new file):
   - Helper script to find existing ScalableTarget ARNs
   - Provides detailed information about existing auto-scaling configuration

## Testing

The solution was tested using:
```bash
./deploy.sh -e staging --dry-run
```

The dry-run completed successfully, validating:
- Template syntax and structure
- Parameter validation
- Conditional logic implementation
- SAM build process

## Benefits

1. **Backward Compatibility**: Existing deployments continue to work
2. **Flexibility**: Can handle both new and existing ScalableTargets
3. **Error Prevention**: Eliminates ScalableTarget conflict errors
4. **Easy Migration**: Simple parameter changes to switch between modes
5. **Debugging Support**: Helper script for identifying existing resources

## Next Steps

1. Run the helper script to find the actual ScalableTarget ARN for staging
2. Update the staging parameter file with the real ARN
3. Deploy to staging to verify the fix works
4. Apply the same pattern to other environments if needed

## Troubleshooting

### If the helper script fails:
- Verify AWS CLI is configured correctly
- Check IAM permissions for `application-autoscaling:DescribeScalableTargets`
- Ensure the cluster and service names are correct

### If deployment still fails:
- Verify the ScalableTarget ARN is correct and accessible
- Check that the existing ScalableTarget is for the same ECS service
- Ensure no other CloudFormation stacks are managing the same resources