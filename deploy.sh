#!/bin/bash

# Multi-Metric ECS Step Scaling Deployment Script
# This script builds and deploys the SAM application for ECS step scaling

set -e

# Default values
ENVIRONMENT="dev"
REGION="eu-west-1"
STACK_NAME=""
PARAMETER_FILE=""
PROFILE=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --environment ENV    Environment (dev, staging, prod) [default: dev]"
    echo "  -r, --region REGION      AWS region [default: us-east-1]"
    echo "  -s, --stack-name NAME    CloudFormation stack name [default: ecs-scaling-ENV]"
    echo "  -p, --parameter-file FILE Parameter file path [default: parameters/ENV.json]"
    echo "  --profile PROFILE        AWS CLI profile to use"
    echo "  --validate-only          Only validate the template, don't deploy"
    echo "  --dry-run               Show what would be deployed without actually deploying"
    echo "  --force-delete          Automatically delete failed stacks without prompting"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Stack Recovery Options:"
    echo "  --force-delete          Automatically delete stacks in ROLLBACK_COMPLETE state"
    echo ""
    echo "Examples:"
    echo "  $0 -e dev -r us-west-2"
    echo "  $0 -e prod -s my-prod-scaling-stack --profile production"
    echo "  $0 --validate-only"
    echo "  $0 -e staging --force-delete  # Auto-delete failed stacks"
}

# Function to validate prerequisites
validate_prerequisites() {
    print_status "Validating prerequisites..."
    
    # Check if SAM CLI is installed
    if ! command -v sam &> /dev/null; then
        print_error "SAM CLI is not installed. Please install it first."
        echo "Installation guide: https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html"
        exit 1
    fi
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check AWS credentials
    if [ -n "$PROFILE" ]; then
        AWS_PROFILE_ARG="--profile $PROFILE"
    else
        AWS_PROFILE_ARG=""
    fi
    
    if ! aws sts get-caller-identity $AWS_PROFILE_ARG &> /dev/null; then
        print_error "AWS credentials not configured or invalid."
        exit 1
    fi
    
    print_success "Prerequisites validated"
}

# Function to validate template
validate_template() {
    print_status "Validating SAM template..."
    
    if sam validate --region $REGION; then
        print_success "Template validation passed"
    else
        print_error "Template validation failed"
        exit 1
    fi
}

# Function to validate parameters
validate_parameters() {
    print_status "Validating parameter file: $PARAMETER_FILE"
    
    if [ ! -f "$PARAMETER_FILE" ]; then
        print_error "Parameter file not found: $PARAMETER_FILE"
        exit 1
    fi
    
    # Check if parameter file is valid JSON
    if ! python3 -m json.tool "$PARAMETER_FILE" > /dev/null 2>&1; then
        print_error "Parameter file is not valid JSON: $PARAMETER_FILE"
        exit 1
    fi
    
    print_success "Parameter file validation passed"
}

# Function to convert JSON parameter file to SAM CLI format
convert_parameters() {
    print_status "Converting parameter file to SAM CLI format..."
    
    if [ ! -f "$PARAMETER_FILE" ]; then
        print_error "Parameter file not found: $PARAMETER_FILE"
        exit 1
    fi
    
    # Parse JSON and convert to key=value format
    PARAMETER_OVERRIDES=$(python3 -c "
import json
import sys

try:
    with open('$PARAMETER_FILE', 'r') as f:
        params = json.load(f)
    
    # Convert to space-separated key=value format
    param_pairs = []
    for param in params:
        key = param['ParameterKey']
        value = param['ParameterValue']
        param_pairs.append(f'{key}={value}')
    
    print(' '.join(param_pairs))
except Exception as e:
    print(f'Error parsing parameter file: {e}', file=sys.stderr)
    sys.exit(1)
")
    
    if [ $? -ne 0 ]; then
        print_error "Failed to parse parameter file"
        exit 1
    fi
    
    print_success "Parameter conversion completed"
}

# Function to build the application
build_application() {
    print_status "Building SAM application..."
    
    if sam build --region $REGION; then
        print_success "Build completed successfully"
    else
        print_error "Build failed"
        exit 1
    fi
}

# Function to check and handle stack state
check_stack_state() {
    print_status "Checking CloudFormation stack state..."
    
    AWS_PROFILE_ARG=""
    if [ -n "$PROFILE" ]; then
        AWS_PROFILE_ARG="--profile $PROFILE"
    fi
    
    STACK_STATUS=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --region $REGION $AWS_PROFILE_ARG --query 'Stacks[0].StackStatus' --output text 2>/dev/null || echo "DOES_NOT_EXIST")
    
    if [ "$STACK_STATUS" = "ROLLBACK_COMPLETE" ]; then
        print_warning "Stack is in ROLLBACK_COMPLETE state and cannot be updated."
        print_status "Options:"
        echo "1. Delete and recreate the stack (recommended)"
        echo "2. Use a different stack name"
        echo "3. Exit and handle manually"
        
        if [ "$FORCE_DELETE" = "true" ]; then
            delete_failed_stack
        else
            read -p "Choose option (1/2/3): " choice
            case $choice in
                1) delete_failed_stack ;;
                2) print_error "Please run with a different stack name using -s option"; exit 1 ;;
                3) print_error "Exiting. Please handle the stack state manually."; exit 1 ;;
                *) print_error "Invalid choice. Exiting."; exit 1 ;;
            esac
        fi
    elif [ "$STACK_STATUS" = "CREATE_FAILED" ] || [ "$STACK_STATUS" = "DELETE_FAILED" ]; then
        print_warning "Stack is in $STACK_STATUS state."
        if [ "$FORCE_DELETE" = "true" ]; then
            delete_failed_stack
        else
            read -p "Delete and recreate stack? (y/N): " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                delete_failed_stack
            else
                print_error "Cannot proceed with stack in $STACK_STATUS state"
                exit 1
            fi
        fi
    elif [ "$STACK_STATUS" != "DOES_NOT_EXIST" ]; then
        print_success "Stack exists in $STACK_STATUS state"
    fi
}

# Function to delete failed stack
delete_failed_stack() {
    print_status "Deleting failed stack: $STACK_NAME"
    
    AWS_PROFILE_ARG=""
    if [ -n "$PROFILE" ]; then
        AWS_PROFILE_ARG="--profile $PROFILE"
    fi
    
    aws cloudformation delete-stack --stack-name $STACK_NAME --region $REGION $AWS_PROFILE_ARG
    
    print_status "Waiting for stack deletion to complete..."
    aws cloudformation wait stack-delete-complete --stack-name $STACK_NAME --region $REGION $AWS_PROFILE_ARG
    
    print_success "Stack deleted successfully"
}

# Function to deploy the application
deploy_application() {
    print_status "Deploying SAM application..."
    print_status "Stack Name: $STACK_NAME"
    print_status "Region: $REGION"
    print_status "Environment: $ENVIRONMENT"
    
    # Check stack state first
    check_stack_state
    
    # Convert parameters first
    convert_parameters
    
    DEPLOY_ARGS="--stack-name $STACK_NAME --region $REGION --parameter-overrides $PARAMETER_OVERRIDES --capabilities CAPABILITY_IAM CAPABILITY_NAMED_IAM --no-fail-on-empty-changeset --resolve-s3"
    
    if [ -n "$PROFILE" ]; then
        DEPLOY_ARGS="$DEPLOY_ARGS --profile $PROFILE"
    fi
    
    if [ "$DRY_RUN" = "true" ]; then
        print_warning "DRY RUN MODE - Would execute:"
        echo "sam deploy $DEPLOY_ARGS"
        return 0
    fi
    
    if sam deploy $DEPLOY_ARGS; then
        print_success "Deployment completed successfully"
        
        # Get stack outputs
        print_status "Retrieving stack outputs..."
        get_stack_outputs
    else
        print_error "Deployment failed"
        exit 1
    fi
}

# Function to get stack outputs
get_stack_outputs() {
    AWS_PROFILE_ARG=""
    if [ -n "$PROFILE" ]; then
        AWS_PROFILE_ARG="--profile $PROFILE"
    fi
    
    OUTPUTS=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --region $REGION $AWS_PROFILE_ARG --query 'Stacks[0].Outputs' --output table 2>/dev/null || echo "")
    
    if [ -n "$OUTPUTS" ]; then
        echo ""
        print_success "Stack Outputs:"
        echo "$OUTPUTS"
        echo ""
        
        # Get dashboard URL
        DASHBOARD_URL=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --region $REGION $AWS_PROFILE_ARG --query 'Stacks[0].Outputs[?OutputKey==`DashboardURL`].OutputValue' --output text 2>/dev/null || echo "")
        
        if [ -n "$DASHBOARD_URL" ] && [ "$DASHBOARD_URL" != "None" ]; then
            print_success "CloudWatch Dashboard: $DASHBOARD_URL"
        fi
    fi
}

# Function to show deployment summary
show_deployment_summary() {
    echo ""
    print_success "=== Deployment Summary ==="
    echo "Environment: $ENVIRONMENT"
    echo "Region: $REGION"
    echo "Stack Name: $STACK_NAME"
    echo "Parameter File: $PARAMETER_FILE"
    if [ -n "$PROFILE" ]; then
        echo "AWS Profile: $PROFILE"
    fi
    echo ""
    print_status "Next Steps:"
    echo "1. Update the parameter file with your actual ECS cluster and service names"
    echo "2. Update the LoadBalancerFullName and TargetGroupFullName parameters"
    echo "3. Monitor the CloudWatch dashboard for scaling activities"
    echo "4. Adjust thresholds based on your application's behavior"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -s|--stack-name)
            STACK_NAME="$2"
            shift 2
            ;;
        -p|--parameter-file)
            PARAMETER_FILE="$2"
            shift 2
            ;;
        --profile)
            PROFILE="$2"
            shift 2
            ;;
        --validate-only)
            VALIDATE_ONLY="true"
            shift
            ;;
        --dry-run)
            DRY_RUN="true"
            shift
            ;;
        --force-delete)
            FORCE_DELETE="true"
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Set default values if not provided
if [ -z "$STACK_NAME" ]; then
    STACK_NAME="ecs-scaling-$ENVIRONMENT"
fi

if [ -z "$PARAMETER_FILE" ]; then
    PARAMETER_FILE="parameters/$ENVIRONMENT.json"
fi

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT. Must be dev, staging, or prod."
    exit 1
fi

# Main execution
print_status "Starting ECS Multi-Metric Step Scaling deployment..."
print_status "Environment: $ENVIRONMENT"
print_status "Region: $REGION"

# Validate prerequisites
validate_prerequisites

# Validate template
validate_template

# Validate parameters
validate_parameters

# If validate-only flag is set, exit here
if [ "$VALIDATE_ONLY" = "true" ]; then
    print_success "Validation completed successfully. Exiting without deployment."
    exit 0
fi

# Build application
build_application

# Deploy application
deploy_application

# Show deployment summary
show_deployment_summary

print_success "Deployment process completed!"